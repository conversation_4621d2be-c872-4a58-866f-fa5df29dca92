"""
文件处理服务
支持多种文件格式的上传、存储和内容提取
"""

import os
import uuid
import mimetypes
import json
from typing import Optional, Dict, Any, List
from pathlib import Path
import base64
import io
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 文件处理相关库
try:
    from PIL import Image
except ImportError:
    Image = None

try:
    import PyPDF2
except ImportError:
    PyPDF2 = None

try:
    from docx import Document
except ImportError:
    Document = None

try:
    import openpyxl
    import pandas as pd
except ImportError:
    openpyxl = None
    pd = None

try:
    import markdown
except ImportError:
    markdown = None

# Marker库导入
try:
    from marker.converters.pdf import PdfConverter
    from marker.models import create_model_dict
    from marker.config.parser import ConfigParser
    from marker.output import text_from_rendered
    from marker.processors.llm.llm_image_description import LLMImageDescriptionProcessor
    from marker.processors.llm.llm_meta import LLMSimpleBlockMetaProcessor
    from marker.services.openai import OpenAIService
    MARKER_AVAILABLE = True
except ImportError:
    MARKER_AVAILABLE = False
    print("⚠️ Marker库未安装，将使用基础文本提取功能")

from fastapi import UploadFile, HTTPException


class FileToMarkdownConverter:
    """文件到Markdown转换器 - 集成Marker和LLM服务"""

    def __init__(self):
        """初始化转换器"""
        self.marker_converter = None
        self.marker_config = None
        self.llm_service = None

        # 初始化Marker模型（如果可用）
        if MARKER_AVAILABLE:
            try:
                self._init_marker_with_llm()
            except Exception as e:
                print(f"⚠️ Marker初始化失败: {e}")
                self.marker_converter = None

    def _init_marker_with_llm(self):
        """初始化Marker模型和LLM服务"""
        if not MARKER_AVAILABLE:
            return

        try:
            # 从环境变量获取LLM配置（优先使用QWEN_VL配置）
            openai_api_key = os.getenv("QWEN_VL_API_KEY") or os.getenv("OPENAI_API_KEY") or os.getenv("API_KEY")
            openai_base_url = os.getenv("QWEN_VL_BASE_URL") or os.getenv("OPENAI_BASE_URL") or os.getenv("BASE_URL", "https://api.deepseek.com/v1")
            openai_model = os.getenv("QWEN_VL_MODEL") or os.getenv("OPENAI_MODEL") or os.getenv("MODEL", "deepseek-chat")

            # 调试信息：显示读取的配置
            print(f"🔧 LLM配置读取:")
            print(f"   API Key: {openai_api_key[:10]}..." if openai_api_key else "   API Key: 未配置")
            print(f"   Base URL: {openai_base_url}")
            print(f"   Model: {openai_model}")

            # 创建增强配置，参考marker_example.py
            config = {
                "output_format": "markdown",
                "output_dir": "output",
                "use_llm": True if openai_api_key else False,  # 只有在有API密钥时才启用LLM
                "disable_image_extraction": False,  # 启用图像提取
                "extract_images": True,
                "paginate": False,
                "languages": ["Chinese", "English"],
                "llm_service": "marker.services.openai.OpenAIService",
                "openai_base_url": openai_base_url,
                "openai_model": openai_model,
                "openai_api_key": openai_api_key
            }

            # 创建配置解析器
            config_parser = ConfigParser(config)
            self.marker_config = config

            # 创建PDF转换器，参考marker_example.py的模式
            # 注意：根据marker_example.py，llm_service参数应该传递字符串而不是对象
            llm_service_str = None
            if openai_api_key:
                llm_service_str = "marker.services.openai.OpenAIService"
                print(f"✅ LLM服务配置成功: {openai_model} @ {openai_base_url}")
                self.llm_service = True  # 标记LLM服务可用
            else:
                print("⚠️ 未配置LLM API密钥，使用基础转换模式")
                config["use_llm"] = False
                self.llm_service = None

            self.marker_converter = PdfConverter(
                config=config,
                artifact_dict=create_model_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer(),
                llm_service=llm_service_str
            )

            print("✅ Marker转换器初始化成功")
            if config.get("use_llm"):
                print("🤖 已启用LLM增强功能")
            else:
                print("📝 使用基础转换功能（未配置LLM）")

        except Exception as e:
            print(f"❌ Marker初始化失败: {e}")
            self.marker_converter = None
            self.llm_service = None

    def convert_to_markdown(self, file_path: str, file_type: str) -> str:
        """
        将文件转换为Markdown格式

        Args:
            file_path: 文件路径
            file_type: 文件MIME类型

        Returns:
            转换后的Markdown文本
        """
        try:
            if file_type == 'application/pdf':
                return self._convert_pdf_to_markdown(file_path)
            elif file_type.startswith('image/'):
                return self._convert_image_to_markdown(file_path)
            elif file_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
                return self._convert_word_to_markdown(file_path)
            elif file_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']:
                return self._convert_excel_to_markdown(file_path)
            elif file_type == 'text/plain':
                return self._convert_text_to_markdown(file_path)
            elif file_type == 'text/markdown':
                return self._read_markdown_file(file_path)
            elif file_type == 'text/csv':
                return self._convert_csv_to_markdown(file_path)
            elif file_type == 'application/json':
                return self._convert_json_to_markdown(file_path)
            else:
                return f"# 不支持的文件类型\n\n文件类型: {file_type}\n\n暂不支持此文件格式的Markdown转换。"

        except Exception as e:
            return f"# 文件转换错误\n\n错误信息: {str(e)}\n\n请检查文件格式是否正确。"

    def _convert_pdf_to_markdown(self, file_path: str) -> str:
        """使用Marker将PDF转换为Markdown（支持LLM增强）"""
        if MARKER_AVAILABLE and self.marker_converter:
            try:
                print(f"🔄 开始使用Marker转换PDF: {file_path}")

                # 使用配置好的转换器进行高质量PDF转换
                rendered = self.marker_converter(file_path)
                markdown_text, _, images = text_from_rendered(rendered)

                # 处理图片引用，提供更好的描述
                if images:
                    print(f"📷 发现 {len(images)} 个图像")
                    for img_key, img_data in images.items():
                        # 如果启用了LLM，图像描述会更详细
                        if self.llm_service:
                            markdown_text = markdown_text.replace(f"![]({img_key})", f"![AI描述的图像]({img_key})")
                        else:
                            markdown_text = markdown_text.replace(f"![]({img_key})", f"![图片]({img_key})")

                # 添加转换信息
                conversion_info = ""
                if self.marker_config.get("use_llm"):
                    conversion_info = "\n\n> 📝 此文档使用AI增强转换，包含智能图像描述和结构分析"
                else:
                    conversion_info = "\n\n> 📝 此文档使用基础转换模式"

                return f"# PDF文档内容{conversion_info}\n\n{markdown_text}"

            except Exception as e:
                print(f"⚠️ Marker转换失败，使用基础方法: {e}")
                return self._convert_pdf_basic(file_path)
        else:
            return self._convert_pdf_basic(file_path)

    def _convert_pdf_basic(self, file_path: str) -> str:
        """基础PDF文本提取"""
        try:
            if not PyPDF2:
                return "# PDF处理错误\n\nPyPDF2库未安装"

            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = []

                for page_num, page in enumerate(pdf_reader.pages, 1):
                    text = page.extract_text()
                    if text.strip():
                        text_content.append(f"## 第 {page_num} 页\n\n{text.strip()}")

                if text_content:
                    return f"# PDF文档内容\n\n" + "\n\n---\n\n".join(text_content)
                else:
                    return "# PDF文档\n\n无法提取文本内容，可能是图片PDF或加密文档。"

        except Exception as e:
            return f"# PDF处理错误\n\n错误信息: {str(e)}"

    def _convert_image_to_markdown(self, file_path: str) -> str:
        """将图片转换为Markdown（支持AI描述）"""
        try:
            if not Image:
                return "# 图片处理错误\n\nPIL库未安装"

            # 如果有Marker转换器且支持LLM，使用AI分析图像
            if MARKER_AVAILABLE and self.marker_converter and self.llm_service:
                try:
                    print(f"🤖 使用AI分析图像: {file_path}")
                    # 使用Marker转换器处理图像（参考marker_example.py）
                    rendered = self.marker_converter(file_path)
                    markdown_text, _, images = text_from_rendered(rendered)

                    if markdown_text.strip():
                        return f"# 图像内容（AI分析）\n\n{markdown_text}\n\n> 🤖 此内容由AI视觉模型分析生成"

                except Exception as e:
                    print(f"⚠️ AI图像分析失败，使用基础方法: {e}")

            # 基础图像处理
            with Image.open(file_path) as img:
                # 获取图片信息
                width, height = img.size
                format_name = img.format or 'Unknown'

                # 转换为base64用于嵌入
                buffer = io.BytesIO()
                img.save(buffer, format=format_name)
                img_base64 = base64.b64encode(buffer.getvalue()).decode()

                markdown_content = f"""# 图片文件

![图片](data:image/{format_name.lower()};base64,{img_base64})

## 图片信息

- **尺寸**: {width} × {height} 像素
- **格式**: {format_name}
- **模式**: {img.mode}

> 📝 此图片已嵌入为base64格式，可以直接在Markdown中显示。
"""
                return markdown_content

        except Exception as e:
            return f"# 图片处理错误\n\n错误信息: {str(e)}"

    def _convert_word_to_markdown(self, file_path: str) -> str:
        """将Word文档转换为Markdown"""
        try:
            if not Document:
                return "# Word文档处理错误\n\npython-docx库未安装"

            doc = Document(file_path)
            markdown_content = ["# Word文档内容\n"]

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    # 简单的样式处理
                    if paragraph.style.name.startswith('Heading'):
                        level = int(paragraph.style.name[-1]) if paragraph.style.name[-1].isdigit() else 1
                        markdown_content.append(f"{'#' * (level + 1)} {text}")
                    else:
                        markdown_content.append(text)
                    markdown_content.append("")

            return "\n".join(markdown_content)

        except Exception as e:
            return f"# Word文档处理错误\n\n错误信息: {str(e)}"

    def _convert_excel_to_markdown(self, file_path: str) -> str:
        """将Excel文件转换为Markdown"""
        try:
            if not pd:
                return "# Excel处理错误\n\npandas库未安装"

            # 读取所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None)
            markdown_content = ["# Excel文档内容\n"]

            for sheet_name, df in excel_data.items():
                markdown_content.append(f"## 工作表: {sheet_name}\n")

                # 转换为Markdown表格
                if not df.empty:
                    markdown_table = df.to_markdown(index=False)
                    markdown_content.append(markdown_table)
                else:
                    markdown_content.append("*此工作表为空*")

                markdown_content.append("")

            return "\n".join(markdown_content)

        except Exception as e:
            return f"# Excel处理错误\n\n错误信息: {str(e)}"

    def _convert_text_to_markdown(self, file_path: str) -> str:
        """将纯文本转换为Markdown"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()

            return f"# 文本文件内容\n\n```\n{content}\n```"

        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    content = file.read()
                return f"# 文本文件内容\n\n```\n{content}\n```"
            except Exception as e:
                return f"# 文本文件处理错误\n\n错误信息: {str(e)}"
        except Exception as e:
            return f"# 文本文件处理错误\n\n错误信息: {str(e)}"

    def _read_markdown_file(self, file_path: str) -> str:
        """读取Markdown文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    return file.read()
            except Exception as e:
                return f"# Markdown文件处理错误\n\n错误信息: {str(e)}"
        except Exception as e:
            return f"# Markdown文件处理错误\n\n错误信息: {str(e)}"

    def _convert_csv_to_markdown(self, file_path: str) -> str:
        """将CSV文件转换为Markdown"""
        try:
            if not pd:
                return "# CSV处理错误\n\npandas库未安装"

            df = pd.read_csv(file_path)
            markdown_table = df.to_markdown(index=False)

            return f"# CSV文件内容\n\n{markdown_table}"

        except Exception as e:
            return f"# CSV处理错误\n\n错误信息: {str(e)}"

    def _convert_json_to_markdown(self, file_path: str) -> str:
        """将JSON文件转换为Markdown"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            # 格式化JSON为可读的Markdown
            json_str = json.dumps(data, indent=2, ensure_ascii=False)

            return f"# JSON文件内容\n\n```json\n{json_str}\n```"

        except Exception as e:
            return f"# JSON处理错误\n\n错误信息: {str(e)}"

    def get_llm_status(self) -> Dict[str, Any]:
        """获取LLM配置状态"""
        if not MARKER_AVAILABLE:
            return {
                "available": False,
                "reason": "Marker库未安装",
                "llm_enabled": False
            }

        if not self.marker_converter:
            return {
                "available": False,
                "reason": "Marker转换器未初始化",
                "llm_enabled": False
            }

        llm_enabled = self.marker_config.get("use_llm", False) if self.marker_config else False

        return {
            "available": True,
            "llm_enabled": llm_enabled,
            "llm_service": "OpenAI兼容服务" if self.llm_service else None,
            "model": self.marker_config.get("openai_model") if self.marker_config else None,
            "base_url": self.marker_config.get("openai_base_url") if self.marker_config else None,
            "features": {
                "image_description": llm_enabled,
                "structure_analysis": llm_enabled,
                "enhanced_extraction": llm_enabled
            }
        }


class FileService:
    """文件处理服务类"""
    
    def __init__(self, upload_dir: str = "uploads"):
        """初始化文件服务"""
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)

        # 初始化Markdown转换器
        self.markdown_converter = FileToMarkdownConverter()
        
        # 支持的文件类型
        self.supported_types = {
            # 图片格式
            'image/jpeg': ['.jpg', '.jpeg'],
            'image/png': ['.png'],
            'image/gif': ['.gif'],
            'image/webp': ['.webp'],
            'image/bmp': ['.bmp'],
            
            # 文档格式
            'application/pdf': ['.pdf'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'application/vnd.ms-excel': ['.xls'],
            
            # 文本格式
            'text/plain': ['.txt'],
            'text/markdown': ['.md'],
            'text/csv': ['.csv'],
            'application/json': ['.json'],
            'text/xml': ['.xml'],
            'text/html': ['.html'],
        }
        
        # 显示LLM状态
        llm_status = self.markdown_converter.get_llm_status()
        if llm_status["available"]:
            if llm_status["llm_enabled"]:
                print(f"📁 文件服务初始化完成 - 🤖 AI增强模式 ({llm_status['model']})")
            else:
                print("📁 文件服务初始化完成 - 📝 基础模式")
        else:
            print(f"📁 文件服务初始化完成 - ⚠️ {llm_status['reason']}")

        print("📁 文件服务初始化完成")

    def is_supported_file(self, file: UploadFile) -> bool:
        """检查文件是否支持"""
        # 检查MIME类型
        if file.content_type in self.supported_types:
            return True
        
        # 检查文件扩展名
        if file.filename:
            ext = Path(file.filename).suffix.lower()
            for mime_type, extensions in self.supported_types.items():
                if ext in extensions:
                    return True
        
        return False

    async def save_file(self, file: UploadFile) -> Dict[str, Any]:
        """保存上传的文件"""
        if not self.is_supported_file(file):
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型: {file.content_type}"
            )
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        original_name = file.filename or "unknown"
        file_ext = Path(original_name).suffix.lower()
        saved_filename = f"{file_id}{file_ext}"
        file_path = self.upload_dir / saved_filename
        
        # 保存文件
        try:
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            # 获取文件信息
            file_info = {
                "file_id": file_id,
                "original_name": original_name,
                "saved_name": saved_filename,
                "file_path": str(file_path),
                "file_size": len(content),
                "content_type": file.content_type,
                "file_ext": file_ext
            }
            
            print(f"📄 文件保存成功: {original_name} -> {saved_filename}")
            return file_info
            
        except Exception as e:
            # 清理失败的文件
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(
                status_code=500, 
                detail=f"文件保存失败: {str(e)}"
            )

    def get_file_content(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取文件内容"""
        file_path = Path(file_info["file_path"])
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")
        
        try:
            content_type = file_info["content_type"]
            file_ext = file_info["file_ext"]
            
            # 根据文件类型提取内容
            if content_type.startswith('image/'):
                return self._extract_image_content(file_path, file_info)
            elif content_type == 'application/pdf':
                return self._extract_pdf_content(file_path, file_info)
            elif content_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
                return self._extract_docx_content(file_path, file_info)
            elif content_type in ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
                return self._extract_xlsx_content(file_path, file_info)
            elif file_ext == '.csv':
                return self._extract_csv_content(file_path, file_info)
            elif file_ext == '.md':
                return self._extract_markdown_content(file_path, file_info)
            elif content_type.startswith('text/'):
                return self._extract_text_content(file_path, file_info)
            else:
                return {
                    "type": "unsupported",
                    "content": "暂不支持预览此文件类型",
                    "file_info": file_info
                }
                
        except Exception as e:
            print(f"❌ 提取文件内容失败: {str(e)}")
            return {
                "type": "error",
                "content": f"读取文件失败: {str(e)}",
                "file_info": file_info
            }

    def _extract_image_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取图片内容"""
        if not Image:
            raise Exception("PIL库未安装，无法处理图片文件")
        try:
            with Image.open(file_path) as img:
                # 转换为base64用于预览
                buffer = io.BytesIO()
                img_format = img.format or 'PNG'
                img.save(buffer, format=img_format)
                img_base64 = base64.b64encode(buffer.getvalue()).decode()
                
                return {
                    "type": "image",
                    "content": f"data:image/{img_format.lower()};base64,{img_base64}",
                    "metadata": {
                        "width": img.width,
                        "height": img.height,
                        "format": img_format,
                        "mode": img.mode
                    },
                    "file_info": file_info
                }
        except Exception as e:
            raise Exception(f"图片处理失败: {str(e)}")

    def _extract_pdf_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取PDF内容"""
        if not PyPDF2:
            raise Exception("PyPDF2库未安装，无法处理PDF文件")
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = ""
                
                for page_num, page in enumerate(pdf_reader.pages):
                    text_content += f"\n--- 第 {page_num + 1} 页 ---\n"
                    text_content += page.extract_text()
                
                return {
                    "type": "pdf",
                    "content": text_content.strip(),
                    "metadata": {
                        "pages": len(pdf_reader.pages),
                        "title": pdf_reader.metadata.get('/Title', '') if pdf_reader.metadata else '',
                        "author": pdf_reader.metadata.get('/Author', '') if pdf_reader.metadata else ''
                    },
                    "file_info": file_info
                }
        except Exception as e:
            raise Exception(f"PDF处理失败: {str(e)}")

    def _extract_docx_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取Word文档内容"""
        if not Document:
            raise Exception("python-docx库未安装，无法处理Word文档")
        try:
            doc = Document(file_path)
            text_content = ""
            
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"
            
            return {
                "type": "docx",
                "content": text_content.strip(),
                "metadata": {
                    "paragraphs": len(doc.paragraphs),
                    "core_properties": {
                        "title": doc.core_properties.title or '',
                        "author": doc.core_properties.author or '',
                        "subject": doc.core_properties.subject or ''
                    }
                },
                "file_info": file_info
            }
        except Exception as e:
            raise Exception(f"Word文档处理失败: {str(e)}")

    def _extract_xlsx_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取Excel内容"""
        if not pd or not openpyxl:
            raise Exception("pandas或openpyxl库未安装，无法处理Excel文件")
        try:
            # 使用pandas读取Excel
            df = pd.read_excel(file_path, sheet_name=None)  # 读取所有工作表
            
            content = {}
            for sheet_name, sheet_df in df.items():
                # 转换为HTML表格用于预览
                html_table = sheet_df.to_html(classes='excel-table', table_id=f'sheet-{sheet_name}')
                content[sheet_name] = {
                    "html": html_table,
                    "rows": len(sheet_df),
                    "columns": len(sheet_df.columns),
                    "column_names": list(sheet_df.columns)
                }
            
            return {
                "type": "xlsx",
                "content": content,
                "metadata": {
                    "sheets": list(df.keys()),
                    "total_sheets": len(df)
                },
                "file_info": file_info
            }
        except Exception as e:
            raise Exception(f"Excel处理失败: {str(e)}")

    def _extract_csv_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取CSV内容"""
        if not pd:
            raise Exception("pandas库未安装，无法处理CSV文件")
        try:
            df = pd.read_csv(file_path)
            html_table = df.to_html(classes='csv-table')
            
            return {
                "type": "csv",
                "content": {
                    "html": html_table,
                    "text": df.to_string(),
                    "rows": len(df),
                    "columns": len(df.columns),
                    "column_names": list(df.columns)
                },
                "metadata": {
                    "shape": df.shape,
                    "dtypes": df.dtypes.to_dict()
                },
                "file_info": file_info
            }
        except Exception as e:
            raise Exception(f"CSV处理失败: {str(e)}")

    def _extract_markdown_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取Markdown内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                md_content = file.read()

            # 转换为HTML
            if markdown:
                html_content = markdown.markdown(md_content, extensions=['tables', 'fenced_code'])
            else:
                html_content = f"<pre>{md_content}</pre>"  # 简单的HTML包装
            
            return {
                "type": "markdown",
                "content": {
                    "markdown": md_content,
                    "html": html_content
                },
                "metadata": {
                    "lines": len(md_content.split('\n')),
                    "characters": len(md_content)
                },
                "file_info": file_info
            }
        except Exception as e:
            raise Exception(f"Markdown处理失败: {str(e)}")

    def _extract_text_content(self, file_path: Path, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取纯文本内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text_content = file.read()
            
            return {
                "type": "text",
                "content": text_content,
                "metadata": {
                    "lines": len(text_content.split('\n')),
                    "characters": len(text_content),
                    "words": len(text_content.split())
                },
                "file_info": file_info
            }
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as file:
                    text_content = file.read()
                return {
                    "type": "text",
                    "content": text_content,
                    "metadata": {
                        "encoding": "gbk",
                        "lines": len(text_content.split('\n')),
                        "characters": len(text_content)
                    },
                    "file_info": file_info
                }
            except Exception as e:
                raise Exception(f"文本文件编码错误: {str(e)}")

    def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        try:
            # 查找文件
            for file_path in self.upload_dir.glob(f"{file_id}.*"):
                file_path.unlink()
                print(f"🗑️ 文件已删除: {file_path.name}")
                return True
            return False
        except Exception as e:
            print(f"❌ 删除文件失败: {str(e)}")
            return False

    def get_supported_types(self) -> Dict[str, List[str]]:
        """获取支持的文件类型"""
        return self.supported_types

    def get_file_markdown(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取文件的Markdown格式内容

        Args:
            file_info: 文件信息字典

        Returns:
            包含Markdown内容的字典
        """
        file_path = Path(file_info["file_path"])

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        try:
            content_type = file_info["content_type"]

            # 使用Markdown转换器
            markdown_content = self.markdown_converter.convert_to_markdown(
                str(file_path),
                content_type
            )

            return {
                "type": "markdown",
                "content": {
                    "markdown": markdown_content,
                    "html": markdown.markdown(markdown_content, extensions=['tables', 'fenced_code']) if markdown else f"<pre>{markdown_content}</pre>"
                },
                "metadata": {
                    "lines": len(markdown_content.split('\n')),
                    "characters": len(markdown_content),
                    "source_type": content_type,
                    "conversion_method": "marker" if MARKER_AVAILABLE else "basic"
                },
                "file_info": file_info
            }

        except Exception as e:
            print(f"❌ 获取Markdown内容失败: {str(e)}")
            return {
                "type": "error",
                "content": f"转换为Markdown失败: {str(e)}",
                "file_info": file_info
            }
