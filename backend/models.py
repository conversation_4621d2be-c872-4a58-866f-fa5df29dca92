"""
Pydantic数据模型定义
定义API请求和响应的数据结构
"""

from typing import Optional, List, Dict, Any  # 类型注解工具
from pydantic import BaseModel, Field  # Pydantic基础模型和字段验证
from datetime import datetime  # 日期时间处理


class ChatRequest(BaseModel):
    """聊天请求模型：定义了聊天请求的数据结构"""
    # 定义字段
    message: str = Field(..., # 必填字段
                         description="用户输入的消息",
                         min_length=1,
                         max_length=50000)  # 增加最大长度以支持附件内容
    conversation_id: Optional[str] = Field(None, # 可选字段  Optional[str]：表示一个值可以是 字符串（str） 或者 None，亦可写成str | None，如下
    # conversation_id : str | None  = Field(None,
                                           description="对话ID，用于维持对话上下文")
    user_id: Optional[str] = Field(None,
                                   description="用户ID")
    attachments: Optional[List[Dict[str, Any]]] = Field(None,
                                                       description="附件列表")
    
    class Config:
        json_schema_extra = {  # OpenAPI示例
            "example": {
                "message": "你好，请介绍一下你自己",
                "conversation_id": "conv_123456",
                "user_id": "user_789",
                "attachments": [
                    {
                        "file_id": "file_123",
                        "original_name": "document.pdf",
                        "content_type": "application/pdf"
                    }
                ]
            }
        }


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str = Field(..., description="AI回复的消息")
    status: str = Field("success", description="响应状态")
    conversation_id: Optional[str] = Field(None, description="对话ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")# default_factory=datetime.now正确（传递函数），default_factory=datetime.now()  # 错误（传递的是调用结果）
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "你好！我是一个智能AI助手，很高兴为您服务。",
                "status": "success",
                "conversation_id": "conv_123456",
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class StreamChunk(BaseModel):
    """流式响应块模型"""
    type: str = Field(..., description="块类型：start, chunk, end, error")
    content: str = Field("", description="块内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    
    class Config:
        json_schema_extra = {
            "example": {
                "type": "chunk",
                "content": "你好！",
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间戳")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "消息内容不能为空",
                "error_code": "INVALID_INPUT",
                "details": {"field": "message", "value": ""},
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    service: str = Field(..., description="服务名称")
    version: Optional[str] = Field(None, description="服务版本")
    uptime: Optional[float] = Field(None, description="运行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间戳")
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "service": "autogen-chat-api",
                "version": "1.0.0",
                "uptime": 3600.5,
                "timestamp": "2024-01-01T12:00:00"
            }
        }


class ConversationHistory(BaseModel):
    """对话历史模型"""
    conversation_id: str = Field(..., description="对话ID")
    messages: List[Dict[str, Any]] = Field(default_factory=list, description="消息列表")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    
    class Config:
        json_schema_extra = {
            "example": {
                "conversation_id": "conv_123456",
                "messages": [
                    {
                        "role": "user",
                        "content": "你好",
                        "timestamp": "2024-01-01T12:00:00"
                    },
                    {
                        "role": "assistant",
                        "content": "你好！很高兴为您服务。",
                        "timestamp": "2024-01-01T12:00:01"
                    }
                ],
                "created_at": "2024-01-01T12:00:00",
                "updated_at": "2024-01-01T12:00:01"
            }
        }


class AgentInfo(BaseModel):
    """代理信息模型"""
    name: str = Field(..., description="代理名称")
    description: str = Field(..., description="代理描述")
    capabilities: List[str] = Field(default_factory=list, description="代理能力列表")
    model: str = Field(..., description="使用的模型")
    status: str = Field("active", description="代理状态")

    class Config:
        json_schema_extra = {
            "example": {
                "name": "intelligent_assistant",
                "description": "智能助手代理",
                "capabilities": ["对话", "问答", "创作", "分析"],
                "model": "deepseek-chat",
                "status": "active"
            }
        }


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    file_id: str = Field(..., description="文件ID")
    original_name: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    content_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")


class FileContentResponse(BaseModel):
    """文件内容响应模型"""
    type: str = Field(..., description="内容类型")
    content: Any = Field(..., description="文件内容")
    metadata: Optional[Dict[str, Any]] = Field(None, description="文件元数据")
    file_info: Dict[str, Any] = Field(..., description="文件信息")
