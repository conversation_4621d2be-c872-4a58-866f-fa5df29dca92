"""
Autogen服务封装
处理与大模型的对话，提供流式和非流式接口
————后端逻辑
"""

import asyncio  # Python异步IO库，用于处理异步操作
import os  # 操作系统接口库，用于读取环境变量等
import json
import time
from typing import AsyncGenerator, Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent  # 从autogen库导入助手代理类和用户代理类
from autogen_agentchat.conditions import SourceMatchTermination, TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage  # 流式消息事件类
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import CancellationToken
from autogen_core.models import ModelFamily  # 模型家族枚举
from autogen_ext.models.openai import OpenAIChatCompletionClient  # OpenAI兼容的客户端，autogen定义好的
from dotenv import load_dotenv  # 用于加载.env环境变量文件

@dataclass
class ConversationMessage:
    """对话消息"""
    id: str
    content: str
    role: str  # 'user' | 'assistant'
    agent_name: Optional[str] = None
    timestamp: float = 0

@dataclass
class Conversation:
    """对话记录"""
    id: str
    title: str
    messages: List[ConversationMessage]
    created_at: float
    updated_at: float

class AutogenService: # 定义了一个名为AutogenService的类，用于封装与大语言模型交互的所有功能。
    """Autogen服务类，封装与大模型的交互逻辑"""

    def __init__(self):
        """初始化Autogen服务"""
        # 加载环境变量
        load_dotenv()

        # 初始化模型客户端
        self.model_client = self._create_model_client()

        # 智能代理团队将按需创建，不在初始化时创建

        # 对话历史存储
        self.conversations: Dict[str, Conversation] = {}
        self.conversations_file = "conversations.json"
        self._load_conversations()

        # 流式生成控制 - 支持多对话并发
        self.conversation_streams: Dict[str, dict] = {}  # 每个对话的流式状态
        self.global_is_streaming = False  # 全局流式状态（用于兼容性）

        # 团队状态管理
        self.team_running = False  # 团队是否正在运行
        self.current_team_conversation = None  # 当前运行团队的对话ID

        self.model_client = self._create_model_client()
        self.feedback_queue = asyncio.Queue()
        self.waiting_for_feedback = False  # 是否正在等待用户反馈
        self.feedback_prompt = ""  # 反馈提示信息
        # 测试API连接
        asyncio.create_task(self._test_api_connection())

        print("🤖 Autogen服务初始化完成")

    async def _test_api_connection(self):
        """测试API连接"""
        try:
            print("🔍 测试API连接...")
            # 发送一个简单的测试请求
            test_message = "Hello"
            async for _ in self.chat_stream(test_message, "test_connection"):
                # 只要能收到第一个响应就说明连接正常
                print("✅ API连接测试成功")
                break
        except Exception as e:
            print(f"⚠️ API连接测试失败: {e}")
            print("💡 请检查网络连接和API配置")

    def _create_model_client(self) -> OpenAIChatCompletionClient:
        """
        创建OpenAI兼容的模型客户端

        Returns:
            OpenAIChatCompletionClient: 配置好的模型客户端
        """
        # 检查API密钥
        api_key = os.getenv("API_KEY")
        if not api_key:
            raise ValueError("❌ API_KEY环境变量未设置，请检查.env文件")

        base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")
        model = os.getenv("MODEL", "deepseek-chat")

        print(f"🔧 初始化模型客户端: {model} @ {base_url}")

        return OpenAIChatCompletionClient(
            model=model,  # 模型名称，默认deepseek-chat
            base_url=base_url,  # API基础URL
            api_key=api_key,  # API密钥
            model_info={  # 模型能力配置，非openai模型，这个参数必须有
                "vision": False,  # 不支持视觉
                "function_calling": True,  # 支持函数调用
                "json_output": True,  # 支持JSON格式输出
                "family": ModelFamily.UNKNOWN,  # 模型家族未知
                "structured_output": True,  # 支持结构化输出
                "multiple_system_messages": True,  # 支持多条系统消息
            },
            # 添加超时和重试配置
            timeout=60.0,  # 60秒超时
            max_retries=3,  # 最大重试3次
        )

    async def put_feedback(self, message: Dict[str, Any]):
        await self.feedback_queue.put(message)

    async def user_input_callback(self, prompt: str, cancellation_token: CancellationToken | None) -> str:
        """
        用户输入回调函数，等待用户反馈

        Args:
            prompt: 提示信息
            cancellation_token: 取消令牌

        Returns:
            str: 用户反馈内容
        """
        print(f"🔔 UserProxyAgent 等待用户反馈")
        print(f"📋 提示信息: {prompt}")

        # 设置等待反馈状态
        self.waiting_for_feedback = True
        self.feedback_prompt = prompt
        print(f"🚩 设置反馈状态: waiting_for_feedback = {self.waiting_for_feedback}")

        try:
            print(f"⏳ 开始等待用户反馈，超时时间: 300秒")
            # 等待用户反馈，设置超时时间
            feedback = await asyncio.wait_for(self.feedback_queue.get(), timeout=300.0)  # 5分钟超时
            content = feedback.get("content", "")
            feedback_type = feedback.get("feedback_type", "general")

            print(f"📝 收到用户反馈: {content[:100]}...")
            print(f"📋 反馈类型: {feedback_type}")

            # 根据反馈类型决定返回内容
            if feedback_type == "approve":
                if content.upper() == "TERMINATE":
                    return "TERMINATE"  # 明确终止时结束对话
                else:
                    return f"用户批准: {content}。请继续下一轮工作。"  # 继续对话
            elif feedback_type == "reject":
                return f"用户拒绝: {content}，请重新生成方案。"
            elif feedback_type == "modify":
                return f"用户要求修改: {content}，请根据反馈进行改进。"
            else:
                return f"用户反馈: {content}。请继续工作。"

        except asyncio.TimeoutError:
            print("⏰ 用户反馈超时，继续对话")
            return "用户未及时反馈，请继续"  # 超时时继续对话而不是终止
        except Exception as e:
            print(f"❌ 获取用户反馈时出错: {e}")
            return "获取反馈时出错，请继续"  # 出错时继续对话
        finally:
            # 清除等待状态
            self.waiting_for_feedback = False
            self.feedback_prompt = ""
            print(f"🏁 清除反馈状态: waiting_for_feedback = {self.waiting_for_feedback}")

    def _create_team(self, conversation_id: str = None):
        """创建全新的智能体团队，不携带任何历史记录"""
        print(f"🔄 开始为对话 {conversation_id} 创建全新的智能体团队（无历史记录）")

        # Create the primary agent.
        primary_agent = AssistantAgent(
            "primary",
            model_client=self.model_client,
            system_message="""
        **# 角色与目标**

        你是一名拥有超过10年经验的资深软件测试架构师，精通各种测试方法论（如：等价类划分、边界值分析、因果图、场景法等），并且对用户体验和系统性能有深刻的理解。你的任务是为我接下来描述的功能模块，设计一份专业、全面、且易于执行的高质量测试用例。

        **例如：**

        * **功能点1：用户名登录**
            * 输入：已注册的用户名/邮箱/手机号 + 密码
            * 校验规则：
                * 用户名/密码不能为空。
                * 用户名需在数据库中存在。
                * 密码需与用户名匹配。
                * 支持“记住我”功能，勾选后7天内免登录。
            * 输出：登录成功，跳转到用户首页。
        * **功能点2：错误处理**
            * 用户名不存在时，提示“用户不存在”。
            * 密码错误时，提示“用户名或密码错误”。
            * 连续输错密码5次，账户锁定30分钟。

        **# 测试要求**

        请遵循以下要求设计测试用例：

        1.  **全面性：**
            * **功能测试：** 覆盖所有在“功能需求与规格”中描述的成功和失败场景。
            * **UI/UX测试：** 确保界面布局、文案、交互符合设计稿和用户习惯。
            * **兼容性测试（如果适用）：** 考虑不同的浏览器（Chrome, Firefox, Safari 最新版）、操作系统（Windows, macOS）和分辨率（1920x1080, 1440x900）。
            * **异常/边界测试：** 使用等价类划分和边界值分析方法，测试各种临界条件和非法输入（例如：超长字符串、特殊字符、空值）。
            * **场景组合测试：** 设计基于实际用户使用路径的端到端（End-to-End）场景。

        2.  **专业性：**
            * 每个测试用例都应遵循标准的格式。
            * 步骤清晰，预期结果明确，不产生歧义。
            * 测试数据需具有代表性。

        3.  **输出格式：**
            * 请使用 **Markdown表格** 格式输出测试用例。
            * 表格应包含以下列：**用例ID (TC-XXX)**、**模块**、**优先级 (高/中/低)**、**测试类型**、**用例标题**、**前置条件**、**测试步骤**、**预期结果**、**实际结果 (留空)**。

        **# 开始设计**

        请基于以上所有信息，开始设计测试用例。

        **重要：测试用例设计完成后，请明确表示"测试用例设计完成，请critic进行评审"，以便下一个智能体继续工作。**
            """,
            model_client_stream=True,
        )

        # Create the critic agent.
        critic_agent = AssistantAgent(
            "critic",
            model_client=self.model_client,
            system_message="""
        ** 角色与目标**

        你是一名拥有超过15年软件质量保证（SQA）经验的测试主管（Test Lead）。你以严谨、细致和注重细节而闻名，曾负责过多个大型复杂项目的质量保障工作。你的核心任务是**评审**我接下来提供的测试用例，找出其中潜在的问题、遗漏和可以改进的地方，以确保测试套件的**高效、全面和易于维护**。

        你的评审目标是：

        1.  **提升测试覆盖率：** 识别未被覆盖的需求点、业务场景或异常路径。
        2.  **增强用例质量：** 确保每个用例都清晰、准确、可执行且具有唯一的测试目的。
        3.  **优化测试效率：** 移除冗余或低价值的用例，并对用例的优先级提出建议。
        4.  **提供可行的改进建议：** 不仅要指出问题，更要提出具体、可操作的修改方案。


        ** 评审维度与指令**

        请你严格按照以下维度，逐一对我提供的测试用例进行全面评审，并生成一份正式的评审报告：

        1.  **清晰性 (Clarity):**

              * **标题和描述：** 用例标题是否清晰地概括了测试目的？
              * **步骤的可执行性：** 测试步骤是否足够具体，不包含模糊不清的指令（如“测试一下”、“随便输入”）？一个不熟悉该功能的新手测试工程师能否独立执行？
              * **预期结果的明确性：** 预期结果是否唯一、明确且可验证？是否描述了关键的断言点（Assertion）？

        2.  **覆盖率 (Coverage):**

              * **需求覆盖：** 是否覆盖了所有明确的功能需求点？（请对照“背景信息”中的需求）
              * **路径覆盖：** 除了“happy path”（成功路径），是否充分覆盖了各种**异常路径**和**分支路径**？
              * **边界值分析：** 对于输入框、数值等，是否考虑了边界值（最小值、最大值、刚好超过/低于边界）？
              * **等价类划分：** 是否合理地划分了有效和无效等价类？有没有遗漏重要的无效输入场景（如：特殊字符、SQL注入、超长字符串、空值、空格等）？
              * **场景组合：** 是否考虑了不同功能组合或真实用户使用场景的端到端测试？

        3.  **正确性 (Correctness):**

              * **前置条件：** 前置条件是否清晰、必要且准确？
              * **业务逻辑：** 用例的设计是否准确反映了业务规则？
              * **预期结果的准确性：** 预期结果是否与需求文档或设计规格完全一致？

        4.  **原子性与独立性 (Atomicity & Independence):**

              * **单一职责：** 每个测试用例是否只验证一个具体的点？（避免一个用例包含过多的验证步骤和目的）
              * **独立性：** 用例之间是否相互独立，可以以任意顺序执行，而不会因为执行顺序导致失败？

        5.  **效率与优先级 (Efficiency & Priority):**

              * **冗余性：** 是否存在重复或冗余的测试用例？
              * **优先级：** 用例的优先级（高/中/低）是否设置得当？高优先级的用例是否覆盖了最核心、风险最高的功能？

        ** 输出格式**

        请以 **Markdown格式** 输出一份结构化的**《测试用例评审报告**。报告应包含以下部分：

          * **1. 总体评价:** 对这份测试用例集的整体质量给出一个简要的总结。
          * **2. 优点 (Strengths):** 列出这些用例中做得好的地方。
          * **3. 待改进项 (Actionable Items):** 以表格形式，清晰地列出每个发现的问题。
              * 表格列：**用例ID (或建议新增)** | **问题描述** | **具体改进建议** | **问题类型 (如：覆盖率、清晰性等)**
          * **4. 遗漏的测试场景建议:** 提出在当前用例集中被忽略的重要测试场景或测试点，建议新增用例。

        ** 开始评审**

        请基于以上所有信息和你的专业经验，开始评审工作，并生成报告。

        **重要：评审完成后，请明确表示"评审完成，请用户确认是否同意以上评审意见"，以便触发用户反馈流程。**

            """,
            model_client_stream=True,
        )

        # 创建用户代理，用于接收用户反馈
        user_proxy = UserProxyAgent(
            name="user_proxy",
            description="用户代理，负责收集用户反馈和确认。在每轮对话结束后必须询问用户意见。",
            input_func=self.user_input_callback,
        )
        print(f"🤖 UserProxyAgent 创建完成: {user_proxy.name}")

        # 可以添加更多智能体
        # optimizer_agent = AssistantAgent(
        #     "optimizer",
        #     model_client=self.model_client,
        #     system_message="你是测试优化专家，负责优化测试用例的执行效率和维护性。",
        #     model_client_stream=True,
        # )

        # security_agent = AssistantAgent(
        #     "security",
        #     model_client=self.model_client,
        #     system_message="你是安全测试专家，负责识别安全漏洞和设计安全测试用例。",
        #     model_client_stream=True,
        # )

        # 智能体列表 - 确保UserProxy在最后，会被自动调用
        agents = [primary_agent, critic_agent, user_proxy]
        print(f"🤖 创建智能体团队: {[agent.name for agent in agents]}")
        # agents = [primary_agent, critic_agent, optimizer_agent, security_agent]  # 启用更多智能体

        # 配置终止条件 - 支持循环反馈
        # 只有当用户明确说"APPROVE"或"TERMINATE"时才终止
        text_termination = TextMentionTermination("TERMINATE")

        # 创建团队 - 支持多轮对话，确保UserProxy参与
        team = RoundRobinGroupChat(
            agents,
            termination_condition=text_termination,
            max_turns=20,  # 允许最多20轮对话，支持多次反馈循环
        )
        print(f"🏗️ 团队创建完成，智能体顺序: {[agent.name for agent in agents]}")
        return team

    def _get_or_create_team(self, conversation_id: str):
        """获取或创建对话专用的团队实例"""
        stream_state = self._get_conversation_stream_state(conversation_id)

        # 如果该对话还没有团队实例，创建一个新的
        if 'team' not in stream_state:
            print(f"🏗️ 为对话 {conversation_id} 创建新的独立团队实例")
            # 确保新对话使用全新的团队实例，不携带任何历史
            stream_state['team'] = self._create_team(conversation_id)
            print(f"✅ 新对话 {conversation_id} 的团队实例创建完成，无历史记录")

        return stream_state['team']

    def _cleanup_team(self, conversation_id: str):
        """清理对话的团队实例"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        if 'team' in stream_state:
            try:
                # 尝试停止团队（如果有stop方法）
                team = stream_state['team']
                if hasattr(team, 'stop'):
                    team.stop()
                print(f"🧹 清理对话 {conversation_id} 的团队实例")
            except Exception as e:
                print(f"⚠️ 清理团队时出现警告: {e}")
            finally:
                del stream_state['team']

    def _load_conversations(self):
        """加载对话历史"""
        try:
            if os.path.exists(self.conversations_file):
                with open(self.conversations_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for conv_id, conv_data in data.items():
                        messages = [ConversationMessage(**msg) for msg in conv_data['messages']]
                        self.conversations[conv_id] = Conversation(
                            id=conv_data['id'],
                            title=conv_data['title'],
                            messages=messages,
                            created_at=conv_data['created_at'],
                            updated_at=conv_data['updated_at']
                        )
        except Exception as e:
            print(f"⚠️ 加载对话历史失败: {e}")

    def _save_conversations(self):
        """保存对话历史"""
        try:
            data = {}
            for conv_id, conv in self.conversations.items():
                data[conv_id] = {
                    'id': conv.id,
                    'title': conv.title,
                    'messages': [asdict(msg) for msg in conv.messages],
                    'created_at': conv.created_at,
                    'updated_at': conv.updated_at
                }
            with open(self.conversations_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存对话历史失败: {e}")

    def get_conversations(self, include_empty: bool = False) -> List[Dict]:
        """
        获取对话列表

        Args:
            include_empty: 是否包含没有用户消息的对话
        """
        conversations = []
        for conv in sorted(self.conversations.values(), key=lambda x: x.updated_at, reverse=True):
            # 检查是否有用户消息
            has_user_messages = any(msg.role == 'user' for msg in conv.messages)

            # 如果不包含空对话，则跳过没有用户消息的对话
            if not include_empty and not has_user_messages:
                continue

            conversations.append({
                'id': conv.id,
                'title': conv.title,
                'created_at': conv.created_at,
                'updated_at': conv.updated_at,
                'message_count': len(conv.messages),
                'user_message_count': len([msg for msg in conv.messages if msg.role == 'user']),
                'has_user_messages': has_user_messages
            })
        return conversations

    def get_conversation(self, conversation_id: str) -> Optional[Dict]:
        """获取指定对话"""
        if conversation_id in self.conversations:
            conv = self.conversations[conversation_id]
            return {
                'id': conv.id,
                'title': conv.title,
                'messages': [asdict(msg) for msg in conv.messages],
                'created_at': conv.created_at,
                'updated_at': conv.updated_at
            }
        return None

    def create_conversation(self, title: str = None) -> str:
        """创建新对话"""
        conv_id = f"conv_{int(time.time() * 1000)}"

        # 如果没有提供标题，生成一个基于时间的标题
        if not title:
            from datetime import datetime
            now = datetime.now()
            title = f"对话 {now.strftime('%m-%d %H:%M')}"

        conversation = Conversation(
            id=conv_id,
            title=title,
            messages=[],
            created_at=time.time(),
            updated_at=time.time()
        )
        self.conversations[conv_id] = conversation
        self._save_conversations()
        return conv_id

    def add_message_to_conversation(self, conversation_id: str, message: ConversationMessage):
        """添加消息到对话"""
        if conversation_id in self.conversations:
            self.conversations[conversation_id].messages.append(message)
            self.conversations[conversation_id].updated_at = time.time()
            self._save_conversations()

    def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            self._save_conversations()
            return True
        return False

    def delete_all_conversations(self) -> int:
        """
        删除所有对话

        Returns:
            int: 删除的对话数量
        """
        count = len(self.conversations)
        self.conversations.clear()
        # 清理所有流式状态
        self.conversation_streams.clear()
        self._save_conversations()
        print(f"🗑️ 已删除所有对话，共 {count} 个")
        return count

    def _get_conversation_stream_state(self, conversation_id: str) -> dict:
        """获取对话的流式状态"""
        if conversation_id not in self.conversation_streams:
            self.conversation_streams[conversation_id] = {
                'current_stream': None,
                'is_streaming': False,
                'is_paused': False,
                'cached_content': []
            }
        return self.conversation_streams[conversation_id]

    def _cleanup_conversation_stream(self, conversation_id: str):
        """清理对话的流式状态"""
        if conversation_id in self.conversation_streams:
            # 先清理团队实例
            self._cleanup_team(conversation_id)
            # 再删除流式状态
            del self.conversation_streams[conversation_id]

    async def chat_stream(self, message: str, conversation_id: Optional[str] = None, retry_count: int = 0) -> AsyncGenerator[dict, None]:
        """
        流式聊天接口 - 多智能体对话

        Args:
            message: 用户输入的消息
            conversation_id: 对话ID
            retry_count: 重试次数

        Yields:
            dict: {'content': str, 'agent_name': str, 'agent_switch'?: bool}
        """
        if not conversation_id:
            conversation_id = f"temp_{int(time.time() * 1000)}"

        stream_state = self._get_conversation_stream_state(conversation_id)
        max_retries = 2  # 最大重试次数

        try:
            stream_state['is_streaming'] = True
            stream_state['is_paused'] = False
            stream_state['cached_content'] = []  # 清空缓存
            self.global_is_streaming = True

            # 保存用户消息到对话历史
            if conversation_id:
                user_msg = ConversationMessage(
                    id=f"msg_{int(time.time() * 1000)}",
                    content=message,
                    role="user",
                    timestamp=time.time()
                )
                self.add_message_to_conversation(conversation_id, user_msg)

            # 获取或创建该对话专用的团队实例
            team = self._get_or_create_team(conversation_id)

            print(f"🚀 启动对话 {conversation_id} 的团队任务")
            stream_result = team.run_stream(task=message)
            stream_state['current_stream'] = stream_result

            # 存储智能体消息内容
            agent_messages = {}
            current_agent = None

            async for event in stream_result:
                # 检查是否被停止
                if not stream_state['is_streaming']:
                    print(f"🛑 对话 {conversation_id} 的生成被停止")
                    break

                # 只处理智能体完成的TextMessage，不使用ModelClientStreamingChunkEvent
                # 因为ModelClientStreamingChunkEvent无法准确区分智能体来源
                if isinstance(event, TextMessage) and hasattr(event, 'source'):
                    agent_name = event.source

                    # 跳过用户消息，只处理AI智能体消息
                    if agent_name == 'user':
                        print(f"⚠️ 跳过用户消息: {agent_name}")
                        continue

                    # 如果是新智能体，发送切换信号
                    if agent_name != current_agent:
                        if current_agent is not None:  # 不是第一个智能体
                            print(f"🔄 智能体切换: {current_agent} -> {agent_name}")
                            yield {'content': '', 'agent_name': agent_name, 'agent_switch': True}
                        current_agent = agent_name

                    # 记录智能体已完成
                    agent_messages[agent_name] = event.content

                    print(f"📝 {agent_name} 智能体完成，内容长度: {len(event.content)} 字符")

                    # 分块发送内容，模拟流式输出
                    if event.content:
                        print(f"📤 处理 {agent_name} 智能体内容: {len(event.content)} 字符")
                        content = event.content
                        chunk_size = 10  # 减少每次发送的字符数

                        for i in range(0, len(content), chunk_size):
                            if not stream_state['is_streaming']:
                                break

                            chunk = content[i:i + chunk_size]
                            chunk_data = {'content': chunk, 'agent_name': agent_name}

                            if stream_state['is_paused']:
                                # 暂停时缓存内容
                                stream_state['cached_content'].append(chunk_data)
                                print(f"💾 对话 {conversation_id} 缓存内容: {len(chunk)} 字符")
                            else:
                                # 正常发送
                                yield chunk_data

                            # 增加延迟，让滚动条跟上
                            await asyncio.sleep(0.08)

                    # 保存智能体消息到对话历史（只保存AI智能体的消息）
                    if conversation_id and agent_name != 'user':
                        ai_msg = ConversationMessage(
                            id=f"msg_{int(time.time() * 1000)}_{agent_name}",
                            content=event.content,
                            role="assistant",
                            agent_name=agent_name,
                            timestamp=time.time()
                        )
                        self.add_message_to_conversation(conversation_id, ai_msg)

        except Exception as e:
            error_message = str(e)
            error_type = type(e).__name__
            print(f"❌ 处理消息时出错 [{error_type}]: {error_message}")

            # 检查是否可以重试
            retryable_errors = ["ReadError", "TimeoutError", "ConnectionError", "httpx"]
            should_retry = (
                retry_count < max_retries and
                any(error in error_type or error in error_message.lower() for error in retryable_errors) and
                "401" not in error_message and "unauthorized" not in error_message.lower()  # 认证错误不重试
            )

            if should_retry:
                print(f"🔄 第 {retry_count + 1} 次重试...")
                yield {'content': f"⚠️ 连接中断，正在重试... ({retry_count + 1}/{max_retries})", 'agent_name': 'system'}
                await asyncio.sleep(2)  # 等待2秒后重试

                # 递归重试
                async for retry_chunk in self.chat_stream(message, conversation_id, retry_count + 1):
                    yield retry_chunk
                return

            # 根据错误类型提供更友好的提示
            friendly_message = "❌ 处理消息时出错"

            if "ReadError" in error_type or "httpx" in error_message.lower():
                friendly_message = "❌ 网络连接中断，请检查网络连接或稍后重试"
            elif "401" in error_message or "unauthorized" in error_message.lower():
                friendly_message = "❌ API密钥无效，请检查配置"
            elif "timeout" in error_message.lower() or "TimeoutError" in error_type:
                friendly_message = "❌ 请求超时，请稍后重试"
            elif "429" in error_message or "rate limit" in error_message.lower():
                friendly_message = "❌ API调用频率超限，请稍后重试"
            elif "500" in error_message or "internal server error" in error_message.lower():
                friendly_message = "❌ API服务器内部错误，请稍后重试"
            elif "connection" in error_message.lower():
                friendly_message = "❌ 无法连接到API服务器，请检查网络或服务器状态"
            else:
                friendly_message = f"❌ 处理消息时出错：{error_message}"

            print(f"🔄 返回友好错误信息: {friendly_message}")
            yield {'content': friendly_message, 'agent_name': 'error'}
        finally:
            stream_state['is_streaming'] = False
            stream_state['is_paused'] = False
            stream_state['current_stream'] = None

            # 清理团队实例
            self._cleanup_team(conversation_id)

            # 检查是否还有其他对话在生成
            self.global_is_streaming = any(
                state['is_streaming'] for state in self.conversation_streams.values()
            )
            print(f"🏁 对话 {conversation_id} 生成完全结束")

    def stop_generation(self, conversation_id: Optional[str] = None):
        """停止生成（可指定对话ID）"""
        if conversation_id:
            # 停止特定对话的生成
            if conversation_id in self.conversation_streams:
                self.conversation_streams[conversation_id]['is_streaming'] = False
                print(f"🛑 停止对话 {conversation_id} 的生成")
        else:
            # 停止所有对话的生成
            for conv_id, state in self.conversation_streams.items():
                state['is_streaming'] = False
                print(f"🛑 停止对话 {conv_id} 的生成")
            self.global_is_streaming = False

    def pause_generation(self, conversation_id: str):
        """暂停前端显示（后端继续运行）"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        stream_state['is_paused'] = True
        print(f"⏸️ 对话 {conversation_id} 前端暂停显示，后端继续运行")

    def continue_generation(self, conversation_id: str):
        """恢复前端显示"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        stream_state['is_paused'] = False
        print(f"▶️ 对话 {conversation_id} 前端恢复显示")

    async def get_cached_content(self, conversation_id: str) -> AsyncGenerator[dict, None]:
        """获取缓存的内容"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        if stream_state['cached_content']:
            print(f"📤 发送对话 {conversation_id} 缓存内容: {len(stream_state['cached_content'])} 块")
            for chunk_data in stream_state['cached_content']:
                yield chunk_data
                await asyncio.sleep(0.02)  # 快速发送缓存内容
            stream_state['cached_content'] = []  # 清空缓存

    def is_generating(self, conversation_id: Optional[str] = None) -> bool:
        """检查是否正在生成"""
        if conversation_id:
            return conversation_id in self.conversation_streams and \
                   self.conversation_streams[conversation_id]['is_streaming']
        return self.global_is_streaming

    async def close(self):
        """关闭服务，清理资源"""
        try:
            if hasattr(self.model_client, 'close'):# hasattr() 用来检查一个对象是否有某个属性或方法，有就返回 True，没有则返回 False，检查有没有close方法
                await self.model_client.close()
            print("🔒 Autogen服务已关闭")
        except Exception as e:
            print(f"⚠️ 关闭服务时出现警告: {e}")


# 创建全局服务实例（单例模式）
_autogen_service_instance = None


def get_autogen_service() -> AutogenService:
    """
    获取Autogen服务实例（单例模式）

    Returns:
        AutogenService: 服务实例
    """
    global _autogen_service_instance  # 使用全局变量_autogen_service_instance来保存唯一实例
    if _autogen_service_instance is None:
        _autogen_service_instance = AutogenService()
    return _autogen_service_instance
# 整个应用程序中只有一个AutogenService实例,任何需要AutogenService的地方都通过此函数获取

if __name__ == "__main__":
    print("AutoGen服务模块")
