"""
Autogen服务封装
处理与大模型的对话，提供流式和非流式接口
————后端逻辑
"""

import asyncio  # Python异步IO库，用于处理异步操作
import os  # 操作系统接口库，用于读取环境变量等
import json
import time
from typing import AsyncGenerator, Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent  # 从autogen库导入助手代理类和用户代理类
from autogen_agentchat.conditions import SourceMatchTermination, TextMentionTermination
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage, UserInputRequestedEvent  # 流式消息事件类
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_core import CancellationToken
from autogen_core.models import ModelFamily  # 模型家族枚举
from autogen_ext.models.openai import OpenAIChatCompletionClient  # OpenAI兼容的客户端，autogen定义好的
from dotenv import load_dotenv  # 用于加载.env环境变量文件

@dataclass
class ConversationMessage:
    """对话消息"""
    id: str
    content: str
    role: str  # 'user' | 'assistant'
    agent_name: Optional[str] = None
    timestamp: float = 0
    attachments: Optional[list] = None

@dataclass
class Conversation:
    """对话记录"""
    id: str
    title: str
    messages: List[ConversationMessage]
    created_at: float
    updated_at: float

class AutogenService: # 定义了一个名为AutogenService的类，用于封装与大语言模型交互的所有功能。
    """Autogen服务类，封装与大模型的交互逻辑"""

    def __init__(self):
        """初始化Autogen服务"""
        # 加载环境变量
        load_dotenv()

        # 初始化模型客户端
        self.model_client = self._create_model_client()

        # Session管理 - 参考demo的实现
        self.sessions: Dict[str, Any] = {}  # 存储每个session的团队实例

        # 对话历史存储
        self.conversations: Dict[str, Conversation] = {}
        self.conversations_file = "conversations.json"
        self._load_conversations()

        # 流式生成控制 - 支持多对话并发
        self.conversation_streams: Dict[str, dict] = {}  # 每个对话的流式状态
        self.global_is_streaming = False  # 全局流式状态（用于兼容性）

        # Session级别的反馈队列管理
        self.session_feedback_queues: Dict[str, asyncio.Queue] = {}
        self.session_feedback_states: Dict[str, dict] = {}  # 每个session的反馈状态

        # 兼容性：保留全局反馈队列
        self.feedback_queue = asyncio.Queue()
        self.waiting_for_feedback = False  # 是否正在等待用户反馈
        self.feedback_prompt = ""  # 反馈提示信息

        # 测试API连接
        asyncio.create_task(self._test_api_connection())

        print("🤖 Autogen服务初始化完成")

    async def _test_api_connection(self):
        """测试API连接"""
        try:
            print("🔍 测试API连接...")
            # 直接测试模型客户端，不创建团队
            from autogen_agentchat.messages import TextMessage
            test_message = TextMessage(content="Hello", source="user")

            # 创建一个简单的智能体进行测试，不包含UserProxyAgent
            from autogen_agentchat.agents import AssistantAgent
            test_agent = AssistantAgent(
                name="test_agent",
                model_client=self.model_client,
                description="测试连接的临时智能体"
            )

            # 发送测试消息
            response = await test_agent.on_messages([test_message], cancellation_token=None)
            if response and response.chat_message:
                print("✅ API连接测试成功")
            else:
                print("⚠️ API连接测试失败: 无响应")

        except Exception as e:
            print(f"⚠️ API连接测试失败: {e}")
            print("💡 请检查网络连接和API配置")

    def _create_model_client(self) -> OpenAIChatCompletionClient:
        """
        创建OpenAI兼容的模型客户端

        Returns:
            OpenAIChatCompletionClient: 配置好的模型客户端
        """
        # 检查API密钥
        api_key = os.getenv("API_KEY")
        if not api_key:
            raise ValueError("❌ API_KEY环境变量未设置，请检查.env文件")

        base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")
        model = os.getenv("MODEL", "deepseek-chat")

        print(f"🔧 初始化模型客户端: {model} @ {base_url}")

        return OpenAIChatCompletionClient(
            model=model,  # 模型名称，默认deepseek-chat
            base_url=base_url,  # API基础URL
            api_key=api_key,  # API密钥
            model_info={  # 模型能力配置，非openai模型，这个参数必须有
                "vision": False,  # 不支持视觉
                "function_calling": True,  # 支持函数调用
                "json_output": True,  # 支持JSON格式输出
                "family": ModelFamily.UNKNOWN,  # 模型家族未知
                "structured_output": True,  # 支持结构化输出
                "multiple_system_messages": True,  # 支持多条系统消息
            },
            # 添加超时和重试配置
            timeout=60.0,  # 60秒超时
            max_retries=3,  # 最大重试3次
        )

    def _get_or_create_session_feedback_queue(self, session_id: str) -> asyncio.Queue:
        """获取或创建session专用的反馈队列"""
        if session_id not in self.session_feedback_queues:
            self.session_feedback_queues[session_id] = asyncio.Queue()
            self.session_feedback_states[session_id] = {
                'waiting_for_feedback': False,
                'feedback_prompt': ''
            }
        return self.session_feedback_queues[session_id]

    def _get_session_feedback_state(self, session_id: str) -> dict:
        """获取session的反馈状态"""
        if session_id not in self.session_feedback_states:
            self.session_feedback_states[session_id] = {
                'waiting_for_feedback': False,
                'feedback_prompt': ''
            }
        return self.session_feedback_states[session_id]

    async def put_feedback(self, message: Dict[str, Any]):
        """提交反馈到指定session或全局队列"""
        session_id = message.get('session_id') or message.get('conversation_id')

        if session_id:
            # 提交到指定session的队列
            session_queue = self._get_or_create_session_feedback_queue(session_id)
            await session_queue.put(message)
            print(f"📝 反馈已提交到session {session_id}")
        else:
            # 兼容性：提交到全局队列
            await self.feedback_queue.put(message)
            print("📝 反馈已提交到全局队列")

    def create_user_input_callback(self, session_id: str):
        """为指定session创建用户输入回调函数"""
        async def user_input_callback(prompt: str, cancellation_token: CancellationToken | None) -> str:
            return await self._user_input_callback_impl(session_id, prompt, cancellation_token)
        return user_input_callback

    async def _user_input_callback_impl(self, session_id: str, prompt: str, cancellation_token: CancellationToken | None) -> str:
        """
        用户输入回调函数实现，等待指定session的用户反馈

        Args:
            session_id: 会话ID
            prompt: 提示信息
            cancellation_token: 取消令牌

        Returns:
            str: 用户反馈内容
        """
        print(f"🔔 Session {session_id} UserProxyAgent 等待用户反馈")
        print(f"📋 提示信息: {prompt}")

        # 获取session专用的反馈队列和状态
        session_queue = self._get_or_create_session_feedback_queue(session_id)
        session_state = self._get_session_feedback_state(session_id)

        # 设置session的等待反馈状态
        session_state['waiting_for_feedback'] = True
        session_state['feedback_prompt'] = prompt

        # 兼容性：同时设置全局状态
        self.waiting_for_feedback = True
        self.feedback_prompt = prompt
        print(f"🚩 设置Session {session_id} 反馈状态: waiting_for_feedback = True")

        try:
            print(f"⏳ Session {session_id} 开始等待用户反馈，超时时间: 300秒")
            # 等待session专用队列的用户反馈
            feedback = await asyncio.wait_for(session_queue.get(), timeout=300.0)  # 5分钟超时
            content = feedback.get("content", "")
            feedback_type = feedback.get("feedback_type", "general")

            print(f"📝 Session {session_id} 收到用户反馈: {content[:100]}...")
            print(f"📋 反馈类型: {feedback_type}")

            # 根据反馈类型返回相应内容
            feedback_responses = {
                "approve": "TERMINATE" if content.upper() == "TERMINATE" else f"用户批准: {content}。请继续下一轮工作。",
                "reject": f"用户拒绝: {content}，请重新生成方案。",
                "modify": f"用户要求修改: {content}，请根据反馈进行改进。"
            }

            return feedback_responses.get(feedback_type, f"用户反馈: {content}。请继续工作。")

        except asyncio.TimeoutError:
            print(f"⏰ Session {session_id} 用户反馈超时，继续对话")
            return "用户未及时反馈，请继续"  # 超时时继续对话而不是终止
        except Exception as e:
            print(f"❌ Session {session_id} 获取用户反馈时出错: {e}")
            return "获取反馈时出错，请继续"  # 出错时继续对话
        finally:
            # 清除session的等待状态
            session_state['waiting_for_feedback'] = False
            session_state['feedback_prompt'] = ""

            # 兼容性：清除全局状态
            self.waiting_for_feedback = False
            self.feedback_prompt = ""
            print(f"🏁 清除Session {session_id} 反馈状态: waiting_for_feedback = False")

    # 兼容性：保留原有的用户输入回调（使用默认session）
    async def user_input_callback(self, prompt: str, cancellation_token: CancellationToken | None) -> str:
        """兼容性方法：使用默认session的用户输入回调"""
        return await self._user_input_callback_impl("default", prompt, cancellation_token)

    def _create_team(self, session_id: str):
        """创建全新的智能体团队，参考demo实现"""
        print(f"🔄 开始为Session {session_id} 创建全新的智能体团队（无历史记录）")

        # Create the primary agent.
        primary_agent = AssistantAgent(
            "primary",
            model_client=self.model_client,
            system_message="""
        **# 角色与目标**

        你是一名拥有超过10年经验的资深软件测试架构师，精通各种测试方法论（如：等价类划分、边界值分析、因果图、场景法等），并且对用户体验和系统性能有深刻的理解。你的任务是为我接下来描述的功能模块，设计一份专业、全面、且易于执行的高质量测试用例。

        **例如：**

        * **功能点1：用户名登录**
            * 输入：已注册的用户名/邮箱/手机号 + 密码
            * 校验规则：
                * 用户名/密码不能为空。
                * 用户名需在数据库中存在。
                * 密码需与用户名匹配。
                * 支持“记住我”功能，勾选后7天内免登录。
            * 输出：登录成功，跳转到用户首页。
        * **功能点2：错误处理**
            * 用户名不存在时，提示“用户不存在”。
            * 密码错误时，提示“用户名或密码错误”。
            * 连续输错密码5次，账户锁定30分钟。

        **# 测试要求**

        请遵循以下要求设计测试用例：

        1.  **全面性：**
            * **功能测试：** 覆盖所有在“功能需求与规格”中描述的成功和失败场景。
            * **UI/UX测试：** 确保界面布局、文案、交互符合设计稿和用户习惯。
            * **兼容性测试（如果适用）：** 考虑不同的浏览器（Chrome, Firefox, Safari 最新版）、操作系统（Windows, macOS）和分辨率（1920x1080, 1440x900）。
            * **异常/边界测试：** 使用等价类划分和边界值分析方法，测试各种临界条件和非法输入（例如：超长字符串、特殊字符、空值）。
            * **场景组合测试：** 设计基于实际用户使用路径的端到端（End-to-End）场景。

        2.  **专业性：**
            * 每个测试用例都应遵循标准的格式。
            * 步骤清晰，预期结果明确，不产生歧义。
            * 测试数据需具有代表性。

        3.  **输出格式：**
            * 请使用 **Markdown表格** 格式输出测试用例。
            * 表格应包含以下列：**用例ID (TC-XXX)**、**模块**、**优先级 (高/中/低)**、**测试类型**、**用例标题**、**前置条件**、**测试步骤**、**预期结果**、**实际结果 (留空)**。

        **# 开始设计**

        请基于以上所有信息，开始设计测试用例。

        **重要：测试用例设计完成后，请明确表示"测试用例设计完成，请critic进行评审"，以便下一个智能体继续工作。**
            """,
            model_client_stream=True,
        )

        # Create the critic agent.
        critic_agent = AssistantAgent(
            "critic",
            model_client=self.model_client,
            system_message="""
        ** 角色与目标**

        你是一名拥有超过15年软件质量保证（SQA）经验的测试主管（Test Lead）。你以严谨、细致和注重细节而闻名，曾负责过多个大型复杂项目的质量保障工作。你的核心任务是**评审**我接下来提供的测试用例，找出其中潜在的问题、遗漏和可以改进的地方，以确保测试套件的**高效、全面和易于维护**。

        你的评审目标是：

        1.  **提升测试覆盖率：** 识别未被覆盖的需求点、业务场景或异常路径。
        2.  **增强用例质量：** 确保每个用例都清晰、准确、可执行且具有唯一的测试目的。
        3.  **优化测试效率：** 移除冗余或低价值的用例，并对用例的优先级提出建议。
        4.  **提供可行的改进建议：** 不仅要指出问题，更要提出具体、可操作的修改方案。


        ** 评审维度与指令**

        请你严格按照以下维度，逐一对我提供的测试用例进行全面评审，并生成一份正式的评审报告：

        1.  **清晰性 (Clarity):**

              * **标题和描述：** 用例标题是否清晰地概括了测试目的？
              * **步骤的可执行性：** 测试步骤是否足够具体，不包含模糊不清的指令（如“测试一下”、“随便输入”）？一个不熟悉该功能的新手测试工程师能否独立执行？
              * **预期结果的明确性：** 预期结果是否唯一、明确且可验证？是否描述了关键的断言点（Assertion）？

        2.  **覆盖率 (Coverage):**

              * **需求覆盖：** 是否覆盖了所有明确的功能需求点？（请对照“背景信息”中的需求）
              * **路径覆盖：** 除了“happy path”（成功路径），是否充分覆盖了各种**异常路径**和**分支路径**？
              * **边界值分析：** 对于输入框、数值等，是否考虑了边界值（最小值、最大值、刚好超过/低于边界）？
              * **等价类划分：** 是否合理地划分了有效和无效等价类？有没有遗漏重要的无效输入场景（如：特殊字符、SQL注入、超长字符串、空值、空格等）？
              * **场景组合：** 是否考虑了不同功能组合或真实用户使用场景的端到端测试？

        3.  **正确性 (Correctness):**

              * **前置条件：** 前置条件是否清晰、必要且准确？
              * **业务逻辑：** 用例的设计是否准确反映了业务规则？
              * **预期结果的准确性：** 预期结果是否与需求文档或设计规格完全一致？

        4.  **原子性与独立性 (Atomicity & Independence):**

              * **单一职责：** 每个测试用例是否只验证一个具体的点？（避免一个用例包含过多的验证步骤和目的）
              * **独立性：** 用例之间是否相互独立，可以以任意顺序执行，而不会因为执行顺序导致失败？

        5.  **效率与优先级 (Efficiency & Priority):**

              * **冗余性：** 是否存在重复或冗余的测试用例？
              * **优先级：** 用例的优先级（高/中/低）是否设置得当？高优先级的用例是否覆盖了最核心、风险最高的功能？

        ** 输出格式**

        请以 **Markdown格式** 输出一份结构化的**《测试用例评审报告**。报告应包含以下部分：

          * **1. 总体评价:** 对这份测试用例集的整体质量给出一个简要的总结。
          * **2. 优点 (Strengths):** 列出这些用例中做得好的地方。
          * **3. 待改进项 (Actionable Items):** 以表格形式，清晰地列出每个发现的问题。
              * 表格列：**用例ID (或建议新增)** | **问题描述** | **具体改进建议** | **问题类型 (如：覆盖率、清晰性等)**
          * **4. 遗漏的测试场景建议:** 提出在当前用例集中被忽略的重要测试场景或测试点，建议新增用例。

        ** 开始评审**

        请基于以上所有信息和你的专业经验，开始评审工作，并生成报告。

        **重要：评审完成后，请明确表示"评审完成，请用户确认是否同意以上评审意见"，以便触发用户反馈流程。**

            """,
            model_client_stream=True,
        )

        # 创建用户代理，使用session专用的回调函数
        user_proxy = UserProxyAgent(
            name="user_proxy",
            description="用户代理，负责收集用户反馈和确认。在每轮对话结束后必须询问用户意见。",
            input_func=self.create_user_input_callback(session_id),
        )
        print(f"🤖 Session {session_id} UserProxyAgent 创建完成: {user_proxy.name}")

        # 可以添加更多智能体
        # optimizer_agent = AssistantAgent(
        #     "optimizer",
        #     model_client=self.model_client,
        #     system_message="你是测试优化专家，负责优化测试用例的执行效率和维护性。",
        #     model_client_stream=True,
        # )

        # security_agent = AssistantAgent(
        #     "security",
        #     model_client=self.model_client,
        #     system_message="你是安全测试专家，负责识别安全漏洞和设计安全测试用例。",
        #     model_client_stream=True,
        # )

        # 采用demo的成功模式：包含UserProxyAgent在RoundRobinGroupChat中
        agents = [primary_agent, critic_agent, user_proxy]
        print(f"🤖 创建智能体团队: {[agent.name for agent in agents]}")

        # 配置终止条件 - 使用APPROVE而不是TERMINATE，参考demo
        text_termination = TextMentionTermination("APPROVE")

        # 创建团队 - 包含UserProxyAgent，参考demo的成功实现
        team = RoundRobinGroupChat(
            agents,
            termination_condition=text_termination,
            max_turns=20,  # 允许多轮对话和反馈循环
        )
        print(f"🏗️ 团队创建完成，智能体顺序: {[agent.name for agent in agents]}")

        # 直接返回团队，参考demo实现
        return team

    def _get_or_create_session(self, session_id: str):
        """获取或创建session，参考demo的实现"""
        if session_id not in self.sessions:
            print(f"🏗️ 为Session {session_id} 创建新的团队实例")
            self.sessions[session_id] = self._create_team(session_id)
            print(f"✅ Session {session_id} 的团队实例创建完成")
        return self.sessions[session_id]

    def clear_session(self, session_id: str):
        """清除session，参考demo的实现"""
        if session_id in self.sessions:
            try:
                # 清理团队实例
                team = self.sessions[session_id]
                if hasattr(team, 'stop'):
                    team.stop()
                del self.sessions[session_id]
                print(f"🧹 清理Session {session_id} 的团队实例")
            except Exception as e:
                print(f"⚠️ 清理Session {session_id} 时出现警告: {e}")

        # 清理session相关的反馈队列和状态
        if session_id in self.session_feedback_queues:
            del self.session_feedback_queues[session_id]
        if session_id in self.session_feedback_states:
            del self.session_feedback_states[session_id]

    def get_session_count(self) -> int:
        """获取当前session数量，参考demo的实现"""
        return len(self.sessions)

    def _get_or_create_team(self, conversation_id: str):
        """获取或创建对话专用的团队实例，使用session管理"""
        # 使用conversation_id作为session_id
        session_id = conversation_id or "default"
        return self._get_or_create_session(session_id)

    def _cleanup_team(self, conversation_id: str):
        """清理对话的团队实例，使用session管理"""
        session_id = conversation_id or "default"
        self.clear_session(session_id)

    def _load_conversations(self):
        """加载对话历史"""
        try:
            if os.path.exists(self.conversations_file):
                with open(self.conversations_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for conv_id, conv_data in data.items():
                        messages = [ConversationMessage(**msg) for msg in conv_data['messages']]
                        self.conversations[conv_id] = Conversation(
                            id=conv_data['id'],
                            title=conv_data['title'],
                            messages=messages,
                            created_at=conv_data['created_at'],
                            updated_at=conv_data['updated_at']
                        )
        except Exception as e:
            print(f"⚠️ 加载对话历史失败: {e}")

    def _save_conversations(self):
        """保存对话历史"""
        try:
            data = {}
            for conv_id, conv in self.conversations.items():
                data[conv_id] = {
                    'id': conv.id,
                    'title': conv.title,
                    'messages': [asdict(msg) for msg in conv.messages],
                    'created_at': conv.created_at,
                    'updated_at': conv.updated_at
                }
            with open(self.conversations_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存对话历史失败: {e}")

    def get_conversations(self, include_empty: bool = False) -> List[Dict]:
        """
        获取对话列表

        Args:
            include_empty: 是否包含没有用户消息的对话
        """
        conversations = []
        for conv in sorted(self.conversations.values(), key=lambda x: x.updated_at, reverse=True):
            # 检查是否有用户消息
            has_user_messages = any(msg.role == 'user' for msg in conv.messages)

            # 如果不包含空对话，则跳过没有用户消息的对话
            if not include_empty and not has_user_messages:
                continue

            conversations.append({
                'id': conv.id,
                'title': conv.title,
                'created_at': conv.created_at,
                'updated_at': conv.updated_at,
                'message_count': len(conv.messages),
                'user_message_count': len([msg for msg in conv.messages if msg.role == 'user']),
                'has_user_messages': has_user_messages
            })
        return conversations

    def get_conversation(self, conversation_id: str) -> Optional[Dict]:
        """获取指定对话"""
        if conversation_id in self.conversations:
            conv = self.conversations[conversation_id]
            return {
                'id': conv.id,
                'title': conv.title,
                'messages': [asdict(msg) for msg in conv.messages],
                'created_at': conv.created_at,
                'updated_at': conv.updated_at
            }
        return None

    def create_conversation(self, title: str = None) -> str:
        """创建新对话"""
        conv_id = f"conv_{int(time.time() * 1000)}"

        # 如果没有提供标题，生成一个基于时间的标题
        if not title:
            from datetime import datetime
            now = datetime.now()
            title = f"对话 {now.strftime('%m-%d %H:%M')}"

        conversation = Conversation(
            id=conv_id,
            title=title,
            messages=[],
            created_at=time.time(),
            updated_at=time.time()
        )
        self.conversations[conv_id] = conversation
        self._save_conversations()
        return conv_id

    def add_message_to_conversation(self, conversation_id: str, message: ConversationMessage):
        """添加消息到对话"""
        if conversation_id in self.conversations:
            self.conversations[conversation_id].messages.append(message)
            self.conversations[conversation_id].updated_at = time.time()
            self._save_conversations()

    def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话"""
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            self._save_conversations()
            return True
        return False

    def delete_all_conversations(self) -> int:
        """
        删除所有对话

        Returns:
            int: 删除的对话数量
        """
        count = len(self.conversations)
        self.conversations.clear()
        # 清理所有流式状态
        self.conversation_streams.clear()
        self._save_conversations()
        print(f"🗑️ 已删除所有对话，共 {count} 个")
        return count

    def _get_conversation_stream_state(self, conversation_id: str) -> dict:
        """获取对话的流式状态"""
        if conversation_id not in self.conversation_streams:
            self.conversation_streams[conversation_id] = {
                'current_stream': None,
                'is_streaming': False,
                'is_paused': False,
                'cached_content': []
            }
        return self.conversation_streams[conversation_id]

    def _cleanup_conversation_stream(self, conversation_id: str):
        """清理对话的流式状态"""
        if conversation_id in self.conversation_streams:
            # 先清理团队实例
            self._cleanup_team(conversation_id)
            # 再删除流式状态
            del self.conversation_streams[conversation_id]

    async def chat_stream(self, message: str, conversation_id: Optional[str] = None, user_message: Optional[str] = None, attachments: Optional[list] = None, retry_count: int = 0) -> AsyncGenerator[dict, None]:
        """
        流式聊天接口 - 多智能体对话

        Args:
            message: 完整的消息内容（包含附件内容）
            conversation_id: 对话ID
            user_message: 用户原始输入（不包含附件内容）
            attachments: 附件列表
            retry_count: 重试次数

        Yields:
            dict: {'content': str, 'agent_name': str, 'agent_switch'?: bool}
        """
        if not conversation_id:
            conversation_id = f"temp_{int(time.time() * 1000)}"

        stream_state = self._get_conversation_stream_state(conversation_id)
        max_retries = 2  # 最大重试次数

        try:
            stream_state['is_streaming'] = True
            stream_state['is_paused'] = False
            stream_state['cached_content'] = []  # 清空缓存
            self.global_is_streaming = True

            # 保存用户消息到对话历史（只保存用户原始输入，不包含附件内容）
            if conversation_id:
                user_msg = ConversationMessage(
                    id=f"msg_{int(time.time() * 1000)}",
                    content=user_message or message,  # 优先使用用户原始输入
                    role="user",
                    timestamp=time.time(),
                    attachments=attachments  # 单独保存附件信息
                )
                self.add_message_to_conversation(conversation_id, user_msg)

            # 获取或创建该对话专用的团队实例
            team = self._get_or_create_team(conversation_id)

            print(f"🚀 启动对话 {conversation_id} 的团队任务")
            stream_result = team.run_stream(task=message)
            stream_state['current_stream'] = stream_result

            # 存储智能体消息内容
            agent_messages = {}
            current_agent = None

            async for event in stream_result:
                # 检查是否被停止
                if not stream_state['is_streaming']:
                    print(f"🛑 对话 {conversation_id} 的生成被停止")
                    break

                # 采用demo的事件处理方式
                if isinstance(event, ModelClientStreamingChunkEvent):
                    # 流式输出的文本块
                    agent_name = getattr(event, 'source', 'unknown')
                    if event.content:
                        chunk_data = {
                            'content': event.content,
                            'agent_name': agent_name
                        }

                        if stream_state['is_paused']:
                            # 暂停时缓存内容
                            stream_state['cached_content'].append(chunk_data)
                            print(f"💾 对话 {conversation_id} 缓存内容: {len(event.content)} 字符")
                        else:
                            # 正常发送
                            yield chunk_data

                elif isinstance(event, UserInputRequestedEvent):
                    # 关键：检测到UserInputRequestedEvent，触发用户反馈弹窗
                    print(f"🔔 Session {conversation_id} 检测到UserInputRequestedEvent: {event.content}")
                    print(f"📋 来源: {event.source}")

                    # 发送用户反馈请求事件给前端
                    yield {
                        'type': 'user_proxy_request',
                        'content': event.content,
                        'agent_name': event.source,
                        'session_id': conversation_id  # 添加session_id
                    }

                    # 设置session专用的等待反馈状态
                    session_state = self._get_session_feedback_state(conversation_id)
                    session_state['waiting_for_feedback'] = True
                    session_state['feedback_prompt'] = event.content

                    # 兼容性：同时设置全局状态
                    self.waiting_for_feedback = True
                    self.feedback_prompt = event.content
                    print(f"🚩 设置Session {conversation_id} 反馈状态: waiting_for_feedback = True")

                elif isinstance(event, TextMessage) and hasattr(event, 'source'):
                    agent_name = event.source

                    # 如果是新智能体，发送切换信号
                    if agent_name != current_agent:
                        if current_agent is not None:  # 不是第一个智能体
                            print(f"🔄 智能体切换: {current_agent} -> {agent_name}")
                            yield {'content': '', 'agent_name': agent_name, 'agent_switch': True}
                        current_agent = agent_name

                    # 记录智能体已完成
                    agent_messages[agent_name] = event.content

                    print(f"📝 {agent_name} 智能体完成，内容长度: {len(event.content)} 字符")

                    # 保存智能体消息到对话历史（只保存AI智能体的消息）
                    if conversation_id and agent_name != 'user':
                        ai_msg = ConversationMessage(
                            id=f"msg_{int(time.time() * 1000)}_{agent_name}",
                            content=event.content,
                            role="assistant",
                            agent_name=agent_name,
                            timestamp=time.time()
                        )
                        self.add_message_to_conversation(conversation_id, ai_msg)

        except Exception as e:
            error_message = str(e)
            error_type = type(e).__name__
            print(f"❌ 处理消息时出错 [{error_type}]: {error_message}")

            # 检查是否可以重试
            retryable_errors = ["ReadError", "TimeoutError", "ConnectionError", "httpx", "RemoteProtocolError", "peer closed connection"]
            is_retryable = any(error in error_type or error in error_message.lower() for error in retryable_errors)
            is_auth_error = "401" in error_message or "unauthorized" in error_message.lower()

            if retry_count < max_retries and is_retryable and not is_auth_error:
                print(f"🔄 第 {retry_count + 1} 次重试...")
                yield {'content': f"⚠️ 连接中断，正在重试... ({retry_count + 1}/{max_retries})", 'agent_name': 'system'}
                await asyncio.sleep(2)

                # 递归重试
                async for retry_chunk in self.chat_stream(message, conversation_id, user_message, attachments, retry_count + 1):
                    yield retry_chunk
                return

            # 错误消息映射表
            error_messages = {
                "ReadError": "❌ 网络连接中断，请检查网络连接或稍后重试",
                "httpx": "❌ 网络连接中断，请检查网络连接或稍后重试",
                "401": "❌ API密钥无效，请检查配置",
                "unauthorized": "❌ API密钥无效，请检查配置",
                "timeout": "❌ 请求超时，请稍后重试",
                "TimeoutError": "❌ 请求超时，请稍后重试",
                "429": "❌ API调用频率超限，请稍后重试",
                "rate limit": "❌ API调用频率超限，请稍后重试",
                "500": "❌ API服务器内部错误，请稍后重试",
                "internal server error": "❌ API服务器内部错误，请稍后重试",
                "connection": "❌ 无法连接到API服务器，请检查网络或服务器状态"
            }

            # 查找匹配的错误消息
            friendly_message = "❌ 处理消息时出错"
            for key, msg in error_messages.items():
                if key in error_type or key in error_message.lower():
                    friendly_message = msg
                    break
            else:
                friendly_message = f"❌ 处理消息时出错：{error_message}"

            print(f"🔄 返回友好错误信息: {friendly_message}")
            yield {'content': friendly_message, 'agent_name': 'error'}
        finally:
            stream_state['is_streaming'] = False
            stream_state['is_paused'] = False
            stream_state['current_stream'] = None

            # 清理团队实例
            self._cleanup_team(conversation_id)

            # 检查是否还有其他对话在生成
            self.global_is_streaming = any(
                state['is_streaming'] for state in self.conversation_streams.values()
            )
            print(f"🏁 对话 {conversation_id} 生成完全结束")

    def stop_generation(self, conversation_id: Optional[str] = None):
        """停止生成（可指定对话ID）"""
        if conversation_id:
            # 停止特定对话的生成
            if conversation_id in self.conversation_streams:
                self.conversation_streams[conversation_id]['is_streaming'] = False
                print(f"🛑 停止对话 {conversation_id} 的生成")
        else:
            # 停止所有对话的生成
            for conv_id, state in self.conversation_streams.items():
                state['is_streaming'] = False
                print(f"🛑 停止对话 {conv_id} 的生成")
            self.global_is_streaming = False

    def pause_generation(self, conversation_id: str):
        """暂停前端显示（后端继续运行）"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        stream_state['is_paused'] = True
        print(f"⏸️ 对话 {conversation_id} 前端暂停显示，后端继续运行")

    def continue_generation(self, conversation_id: str):
        """恢复前端显示"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        stream_state['is_paused'] = False
        print(f"▶️ 对话 {conversation_id} 前端恢复显示")

    async def get_cached_content(self, conversation_id: str) -> AsyncGenerator[dict, None]:
        """获取缓存的内容"""
        stream_state = self._get_conversation_stream_state(conversation_id)
        if stream_state['cached_content']:
            print(f"📤 发送对话 {conversation_id} 缓存内容: {len(stream_state['cached_content'])} 块")
            for chunk_data in stream_state['cached_content']:
                yield chunk_data
                await asyncio.sleep(0.02)  # 快速发送缓存内容
            stream_state['cached_content'] = []  # 清空缓存

    def is_generating(self, conversation_id: Optional[str] = None) -> bool:
        """检查是否正在生成"""
        if conversation_id:
            return conversation_id in self.conversation_streams and \
                   self.conversation_streams[conversation_id]['is_streaming']
        return self.global_is_streaming

    async def close(self):
        """关闭服务，清理资源"""
        if hasattr(self.model_client, 'close'):  # 检查是否有close方法
            await self.model_client.close()
        print("🔒 Autogen服务已关闭")


# 创建全局服务实例（单例模式）
_autogen_service_instance = None


def get_autogen_service() -> AutogenService:
    """
    获取Autogen服务实例（单例模式）

    Returns:
        AutogenService: 服务实例
    """
    global _autogen_service_instance  # 使用全局变量_autogen_service_instance来保存唯一实例
    if _autogen_service_instance is None:
        _autogen_service_instance = AutogenService()
    return _autogen_service_instance
# 整个应用程序中只有一个AutogenService实例,任何需要AutogenService的地方都通过此函数获取

if __name__ == "__main__":
    print("AutoGen服务模块")
