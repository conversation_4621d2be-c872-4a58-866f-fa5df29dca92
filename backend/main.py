"""
FastAPI后端主应用
提供SSE流式输出接口，支持与Autogen代理的实时对话
"""

import asyncio  # Python异步IO库
import json  # JSON处理
from contextlib import asynccontextmanager  # 异步上下文管理器:asynccontextmanager：用于创建异步的上下文管理器，管理应用生命周期
from typing import AsyncGenerator  # 异步生成器类型注解:AsyncGenerator：定义异步生成器类型，用于流式响应

from fastapi import FastAPI, HTTPException, UploadFile, File, Query  # FastAPI核心
from fastapi.middleware.cors import CORSMiddleware  # CORS中间件
from fastapi.responses import StreamingResponse  # 流式响应:StreamingResponse：FastAPI的流式响应类，支持SSE(Server-Sent Events)
from pydantic import BaseModel  # 数据验证

from autogen_service import AutogenService  # 之前定义的Autogen服务
from file_service import FileService  # 文件处理服务
from models import ChatRequest, ChatResponse, FileUploadResponse, FileContentResponse  # 之前定义的数据模型
from typing import Optional


# 全局变量
autogen_service = None # 全局Autogen服务实例
file_service = None # 全局文件服务实例


@asynccontextmanager # 异步上下文管理器
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global autogen_service, file_service

    # 启动时初始化
    print("🚀 Autogen Chat API 启动成功!")
    print("📡 支持SSE流式输出")
    print("🤖 Autogen代理已就绪")
    print("📁 文件服务已就绪")

    autogen_service = AutogenService() # 初始化Autogen服务
    file_service = FileService() # 初始化文件服务

    yield # 这里会暂停，直到应用关闭  ——语句将函数分为启动和关闭两部分（@asynccontextmanager将函数转换为异步生成器，yield是生成值的点位）

    # 关闭时清理
    if autogen_service:
        await autogen_service.close()
    print("👋 Autogen Chat API 已关闭")
"""  
启动阶段（yield之前）
    执行所有初始化逻辑（如autogen_service = AutogenService()）
    此时FastAPI应用已准备好接收请求
    相当于传统同步代码的__enter__阶段
运行阶段（yield时刻）
    代码执行在此暂停
    控制权交还给FastAPI框架
    应用开始正常处理HTTP请求
    这个暂停状态可能持续数天/数月（直到服务终止）
关闭阶段（yield之后）
    收到终止信号（如Ctrl+C）后恢复执行
    执行清理逻辑（await autogen_service.close()）
    相当于传统同步代码的__exit__阶段
"""
# 创建FastAPI应用实例
app = FastAPI(
    title="Autogen Chat API",
    description="基于Autogen的智能对话API，支持SSE流式输出",
    version="1.0.0",
    lifespan=lifespan  # 传入生命周期管理器
)

# 配置CORS中间件，允许前端跨域访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源(生产环境应限制)，生产环境中应该指定具体的前端域名
    allow_credentials=True, # 允许凭据(cookies等)，前端需要设置withCredentials
    allow_methods=["*"], # 允许所有HTTP方法
    allow_headers=["*"], # 允许所有请求头
)




@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "Autogen Chat API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "autogen-chat-api"}


async def generate_chat_stream(request: ChatRequest) -> AsyncGenerator[str, None]: # 流式聊天核心逻辑
    """
    生成聊天流式响应

    Args:
        request: 聊天请求对象

    Yields:
        str: SSE格式的数据流
    """
    try:
        # 发送开始事件
        yield f"data: {json.dumps({'type': 'start', 'content': ''}, ensure_ascii=False)}\n\n" # ensure_ascii=False确保中文不被转义

        current_agent = None  # 跟踪当前智能体

        # 获取Autogen代理的流式响应
        conversation_id = getattr(request, 'conversation_id', None)
        async for chunk_data in autogen_service.chat_stream(request.message, conversation_id):
            agent_name = chunk_data.get('agent_name')
            content = chunk_data.get('content', '')
            is_agent_switch = chunk_data.get('agent_switch', False)

            # 如果是智能体切换信号
            if is_agent_switch:
                print(f"🔄 收到智能体切换信号: {agent_name}")
                # 发送新消息开始信号
                yield f"data: {json.dumps({'type': 'message_start', 'content': '', 'agentName': agent_name}, ensure_ascii=False)}\n\n"
                current_agent = agent_name
                continue

            # 检查智能体是否发生变化（备用检测）
            if agent_name and agent_name != current_agent:
                # 如果不是第一个智能体，发送新消息开始信号
                if current_agent is not None:
                    print(f"🔄 智能体从 {current_agent} 切换到 {agent_name}")
                    yield f"data: {json.dumps({'type': 'message_start', 'content': '', 'agentName': agent_name}, ensure_ascii=False)}\n\n"

                current_agent = agent_name

            # 发送内容数据
            if content:
                data = {
                    "type": "chunk",
                    "content": content,
                    "agentName": agent_name
                }
                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

        # 发送结束事件
        yield f"data: {json.dumps({'type': 'end', 'content': ''}, ensure_ascii=False)}\n\n"

    except Exception as e:
        # 发送错误事件
        error_data = {
            "type": "error",
            "content": f"处理消息时发生错误: {str(e)}"
        }
        yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"


@app.post("/chat/stream")
async def chat_stream(request: ChatRequest):
    """
    流式聊天接口
    
    Args:
        request: 聊天请求，包含用户消息
        
    Returns:
        StreamingResponse: SSE流式响应
    """
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="消息内容不能为空")
    
    return StreamingResponse(
        generate_chat_stream(request),
        media_type="text/plain; charset=utf-8",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


# 对话管理API
@app.get("/conversations")
async def get_conversations(include_empty: bool = False):
    """
    获取对话列表

    Args:
        include_empty: 是否包含没有用户消息的对话，默认为False
    """
    return {"conversations": autogen_service.get_conversations(include_empty=include_empty)}

@app.post("/conversations")
async def create_conversation(title: str = "新对话"):
    """创建新对话"""
    conversation_id = autogen_service.create_conversation(title)
    return {"conversation_id": conversation_id}

@app.get("/conversations/{conversation_id}")
async def get_conversation(conversation_id: str):
    """获取指定对话"""
    conversation = autogen_service.get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="对话不存在")
    return conversation

@app.delete("/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str):
    """删除对话"""
    success = autogen_service.delete_conversation(conversation_id)
    if not success:
        raise HTTPException(status_code=404, detail="对话不存在")
    return {"message": "对话已删除"}

@app.delete("/conversations")
async def delete_all_conversations():
    """删除所有对话"""
    count = autogen_service.delete_all_conversations()
    return {"message": f"已删除所有对话，共 {count} 个"}

# 生成控制API
@app.post("/chat/stop")
async def stop_generation(conversation_id: Optional[str] = None):
    """停止生成"""
    autogen_service.stop_generation(conversation_id)
    return {"message": f"生成已停止 {f'(对话: {conversation_id})' if conversation_id else '(全部对话)'}"}

@app.post("/chat/continue")
async def continue_generation(conversation_id: Optional[str] = Query(None)):
    """恢复前端显示并获取缓存内容"""
    if not conversation_id:
        raise HTTPException(status_code=400, detail="缺少conversation_id参数")

    autogen_service.continue_generation(conversation_id)

    async def generate_cached_stream():
        """生成缓存内容的流"""
        try:
            async for chunk_data in autogen_service.get_cached_content(conversation_id):
                agent_name = chunk_data.get('agent_name')
                content = chunk_data.get('content', '')

                # 构造SSE数据
                sse_data = {
                    "agent_name": agent_name,
                    "content": content
                }
                yield f"data: {json.dumps(sse_data, ensure_ascii=False)}\n\n"

        except Exception as e:
            error_data = {
                "agent_name": "error",
                "content": f"获取缓存内容时发生错误: {str(e)}"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

    return StreamingResponse(
        generate_cached_stream(),
        media_type="text/plain; charset=utf-8",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.get("/chat/status")
async def get_generation_status(conversation_id: Optional[str] = None):
    """获取生成状态"""
    return {
        "is_generating": autogen_service.is_generating(conversation_id),
        "conversation_id": conversation_id
    }


# 用户反馈API
class UserFeedbackRequest(BaseModel):
    """用户反馈请求模型"""
    content: str
    conversation_id: Optional[str] = None
    feedback_type: str = "general"  # general, approval, modification, etc.


@app.post("/chat/feedback")
async def submit_user_feedback(request: UserFeedbackRequest):
    """
    提交用户反馈给UserProxyAgent

    Args:
        request: 用户反馈请求

    Returns:
        dict: 提交结果
    """
    try:
        feedback_data = {
            "content": request.content,
            "conversation_id": request.conversation_id,
            "feedback_type": request.feedback_type,
            "timestamp": asyncio.get_event_loop().time()
        }

        # 将反馈放入队列
        await autogen_service.put_feedback(feedback_data)

        return {
            "message": "用户反馈已提交",
            "feedback_type": request.feedback_type,
            "conversation_id": request.conversation_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交反馈失败: {str(e)}")


@app.get("/chat/feedback/status")
async def get_feedback_status():
    """
    获取反馈队列状态

    Returns:
        dict: 反馈队列状态
    """
    try:
        queue_size = autogen_service.feedback_queue.qsize()
        return {
            "queue_size": queue_size,
            "is_waiting_feedback": queue_size == 0,
            "message": "反馈队列状态"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取反馈状态失败: {str(e)}")


# 文件管理API
@app.post("/files/upload", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """
    上传文件接口

    Args:
        file: 上传的文件

    Returns:
        FileUploadResponse: 文件上传结果
    """
    try:
        file_info = await file_service.save_file(file)
        return FileUploadResponse(
            file_id=file_info["file_id"],
            original_name=file_info["original_name"],
            file_size=file_info["file_size"],
            content_type=file_info["content_type"]
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@app.get("/files/{file_id}/content", response_model=FileContentResponse)
async def get_file_content(file_id: str):
    """
    获取文件内容接口

    Args:
        file_id: 文件ID

    Returns:
        FileContentResponse: 文件内容
    """
    try:
        # 构造文件信息（实际应用中应该从数据库获取）
        file_path = file_service.upload_dir / f"{file_id}.*"
        matching_files = list(file_service.upload_dir.glob(f"{file_id}.*"))

        if not matching_files:
            raise HTTPException(status_code=404, detail="文件不存在")

        file_path = matching_files[0]
        file_ext = file_path.suffix.lower()

        # 根据文件扩展名推断MIME类型
        content_type = "application/octet-stream"  # 默认类型
        if file_ext in ['.jpg', '.jpeg']:
            content_type = "image/jpeg"
        elif file_ext == '.png':
            content_type = "image/png"
        elif file_ext == '.gif':
            content_type = "image/gif"
        elif file_ext == '.webp':
            content_type = "image/webp"
        elif file_ext == '.bmp':
            content_type = "image/bmp"
        elif file_ext == '.pdf':
            content_type = "application/pdf"
        elif file_ext == '.docx':
            content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif file_ext == '.doc':
            content_type = "application/msword"
        elif file_ext == '.xlsx':
            content_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif file_ext == '.xls':
            content_type = "application/vnd.ms-excel"
        elif file_ext == '.txt':
            content_type = "text/plain"
        elif file_ext == '.md':
            content_type = "text/markdown"
        elif file_ext == '.csv':
            content_type = "text/csv"
        elif file_ext == '.json':
            content_type = "application/json"
        elif file_ext == '.xml':
            content_type = "text/xml"
        elif file_ext == '.html':
            content_type = "text/html"

        file_info = {
            "file_id": file_id,
            "file_path": str(file_path),
            "content_type": content_type,
            "file_ext": file_ext
        }

        content_data = file_service.get_file_content(file_info)
        return FileContentResponse(**content_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文件内容失败: {str(e)}")


@app.delete("/files/{file_id}")
async def delete_file(file_id: str):
    """
    删除文件接口

    Args:
        file_id: 文件ID

    Returns:
        dict: 删除结果
    """
    success = file_service.delete_file(file_id)
    if not success:
        raise HTTPException(status_code=404, detail="文件不存在")
    return {"message": "文件已删除"}


@app.get("/files/supported-types")
async def get_supported_file_types():
    """
    获取支持的文件类型

    Returns:
        dict: 支持的文件类型列表
    """
    return {
        "supported_types": file_service.get_supported_types(),
        "message": "支持的文件类型列表"
    }


if __name__ == "__main__":
    import uvicorn
    
    # 启动服务器
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式下启用热重载
        log_level="info"
    )
