@echo off
echo ========================================
echo    Autogen Chat 一键启动脚本
echo ========================================
echo.

echo 🚀 正在启动 Autogen Chat 系统...
echo.

echo 📋 检查系统环境...

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Python，请安装 Python 3.8+
    pause
    exit /b 1
)
echo ✅ Python 环境正常

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到 Node.js，请安装 Node.js 16+
    pause
    exit /b 1
)
echo ✅ Node.js 环境正常

echo.
echo 🔧 准备后端服务...

:: 检查后端环境
if not exist backend\.env (
    echo ❌ 错误: 后端环境变量文件不存在
    echo 请确保 backend\.env 文件存在并配置正确
    pause
    exit /b 1
)

:: 安装后端依赖
cd backend
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装后端依赖...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
)
cd ..

echo.
echo 🎨 准备前端应用...

:: 安装前端依赖
cd frontend
if not exist node_modules (
    echo 📦 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)
cd ..

echo.
echo 🚀 启动服务...

:: 启动后端服务（后台运行）
echo 启动后端服务...
start "Autogen Chat Backend" cmd /k "cd backend && python main.py"

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 启动前端服务
echo 启动前端应用...
start "Autogen Chat Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ========================================
echo    🎉 Autogen Chat 启动完成！
echo ========================================
echo.
echo 📍 服务地址:
echo   前端应用: http://localhost:3000
echo   后端API:  http://localhost:8000
echo   API文档:  http://localhost:8000/docs
echo.
echo 💡 提示:
echo   - 前端和后端服务已在新窗口中启动
echo   - 请等待几秒钟让服务完全启动
echo   - 按 Ctrl+C 可以停止对应的服务
echo.
echo 🔧 如果遇到问题:
echo   1. 检查端口 3000 和 8000 是否被占用
echo   2. 确认网络连接正常
echo   3. 查看各服务窗口的错误信息
echo.

pause
