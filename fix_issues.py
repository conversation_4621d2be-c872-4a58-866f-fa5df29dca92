#!/usr/bin/env python3
"""
问题修复脚本
自动修复常见的配置和依赖问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True,
            check=check
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr


def check_python():
    """检查Python环境"""
    print("🐍 检查Python环境...")
    
    # 检查Python版本
    version = sys.version_info
    if version >= (3, 8):
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("请安装Python 3.8或更高版本")
        return False


def check_node():
    """检查Node.js环境"""
    print("📦 检查Node.js环境...")
    
    success, stdout, stderr = run_command("node --version", check=False)
    if success:
        version = stdout.strip()
        print(f"✅ Node.js版本: {version}")
        return True
    else:
        print("❌ 未找到Node.js")
        print("请安装Node.js 16或更高版本")
        return False


def fix_backend_dependencies():
    """修复后端依赖"""
    print("🔧 修复后端依赖...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 检查requirements.txt
    requirements_file = backend_dir / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt不存在")
        return False
    
    # 安装依赖
    print("📦 安装Python依赖...")
    success, stdout, stderr = run_command(
        "pip install -r requirements.txt", 
        cwd=backend_dir
    )
    
    if success:
        print("✅ Python依赖安装成功")
        return True
    else:
        print(f"❌ Python依赖安装失败: {stderr}")
        return False


def fix_frontend_dependencies():
    """修复前端依赖"""
    print("🔧 修复前端依赖...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 检查package.json
    package_file = frontend_dir / "package.json"
    if not package_file.exists():
        print("❌ package.json不存在")
        return False
    
    # 删除node_modules和package-lock.json（如果存在）
    node_modules = frontend_dir / "node_modules"
    package_lock = frontend_dir / "package-lock.json"
    
    if node_modules.exists():
        print("🗑️ 清理旧的node_modules...")
        shutil.rmtree(node_modules)
    
    if package_lock.exists():
        print("🗑️ 清理旧的package-lock.json...")
        package_lock.unlink()
    
    # 安装依赖
    print("📦 安装Node.js依赖...")
    success, stdout, stderr = run_command("npm install", cwd=frontend_dir)
    
    if success:
        print("✅ Node.js依赖安装成功")
        return True
    else:
        print(f"❌ Node.js依赖安装失败: {stderr}")
        # 尝试使用yarn
        print("🔄 尝试使用yarn...")
        success, stdout, stderr = run_command("yarn install", cwd=frontend_dir, check=False)
        if success:
            print("✅ 使用yarn安装成功")
            return True
        else:
            print(f"❌ yarn安装也失败: {stderr}")
            return False


def fix_env_files():
    """修复环境变量文件"""
    print("🔧 检查环境变量文件...")
    
    # 检查后端.env文件
    backend_env = Path("backend/.env")
    if not backend_env.exists():
        print("⚠️ 后端.env文件不存在，创建示例文件...")
        env_content = """MODEL=deepseek-chat
BASE_URL=https://api.deepseek.com/v1
API_KEY=your-api-key-here
"""
        with open(backend_env, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 已创建backend/.env示例文件，请填入正确的API_KEY")
    else:
        print("✅ 后端.env文件存在")
    
    # 检查前端.env文件
    frontend_env = Path("frontend/.env")
    if not frontend_env.exists():
        print("⚠️ 前端.env文件不存在，创建示例文件...")
        env_content = """VITE_API_URL=http://localhost:8000
VITE_APP_TITLE=Autogen Chat
VITE_APP_VERSION=1.0.0
"""
        with open(frontend_env, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 已创建frontend/.env文件")
    else:
        print("✅ 前端.env文件存在")


def fix_port_conflicts():
    """检查端口冲突"""
    print("🔍 检查端口占用...")
    
    # 检查8000端口
    success, stdout, stderr = run_command("netstat -an | findstr :8000", check=False)
    if success and stdout.strip():
        print("⚠️ 端口8000可能被占用")
        print("如果后端启动失败，请检查端口占用情况")
    else:
        print("✅ 端口8000可用")
    
    # 检查3000端口
    success, stdout, stderr = run_command("netstat -an | findstr :3000", check=False)
    if success and stdout.strip():
        print("⚠️ 端口3000可能被占用")
        print("如果前端启动失败，请检查端口占用情况")
    else:
        print("✅ 端口3000可用")


def create_startup_scripts():
    """创建启动脚本"""
    print("📝 检查启动脚本...")
    
    scripts = [
        "start_all.bat",
        "start_backend.bat", 
        "start_frontend.bat"
    ]
    
    for script in scripts:
        if Path(script).exists():
            print(f"✅ {script} 存在")
        else:
            print(f"⚠️ {script} 不存在")


def main():
    """主修复函数"""
    print("=" * 50)
    print("🔧 Autogen Chat 问题修复工具")
    print("=" * 50)
    
    issues_found = False
    
    # 检查基础环境
    if not check_python():
        issues_found = True
    
    if not check_node():
        issues_found = True
    
    if issues_found:
        print("\n❌ 基础环境有问题，请先解决环境问题")
        return
    
    # 修复配置文件
    fix_env_files()
    
    # 修复依赖
    print("\n" + "=" * 30)
    if not fix_backend_dependencies():
        print("❌ 后端依赖修复失败")
        issues_found = True
    
    if not fix_frontend_dependencies():
        print("❌ 前端依赖修复失败")
        issues_found = True
    
    # 检查端口
    print("\n" + "=" * 30)
    fix_port_conflicts()
    
    # 检查启动脚本
    print("\n" + "=" * 30)
    create_startup_scripts()
    
    print("\n" + "=" * 50)
    if not issues_found:
        print("🎉 修复完成！系统应该可以正常运行了")
        print("\n💡 下一步:")
        print("1. 确保backend/.env文件中的API_KEY正确")
        print("2. 运行: python test_system.py 进行测试")
        print("3. 运行: start_all.bat 启动系统")
    else:
        print("⚠️ 发现一些问题，请根据上述提示进行修复")
    print("=" * 50)


if __name__ == "__main__":
    main()
