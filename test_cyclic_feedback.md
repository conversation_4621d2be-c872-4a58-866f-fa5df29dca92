# 循环反馈机制测试

## 测试目标
验证以下两个关键功能：
1. 等Critic智能体完成输出后才弹出反馈界面
2. 支持多轮循环反馈，直到用户明确结束对话

## 测试用例

### 测试用例1：等待Critic完成后弹出
**输入消息**："请设计一个用户登录功能的测试用例，包含正常流程和异常处理"

**预期流程**：
1. ✅ Primary智能体生成测试用例
2. ✅ Critic智能体开始评审
3. ⏳ 等待Critic完全完成输出
4. ✅ Critic输出完成后弹出反馈界面
5. ✅ 反馈界面包含4个选项：批准继续、需要修改、拒绝方案、结束对话

**验证要点**：
- 反馈弹窗不会在Critic输出过程中出现
- 只有在Critic完全停止流式输出后才弹出
- 前端日志显示"✅ Critic智能体输出完成，显示反馈弹窗"

### 测试用例2：循环反馈机制
**第一轮反馈**：选择"需要修改" + "请增加边界条件测试"

**预期效果**：
1. ✅ Primary智能体根据反馈改进测试用例
2. ✅ Critic智能体重新评审改进后的内容
3. ✅ 再次弹出反馈界面（第二轮）

**第二轮反馈**：选择"拒绝方案" + "测试覆盖度不够"

**预期效果**：
1. ✅ Primary智能体重新生成更全面的测试用例
2. ✅ Critic智能体再次评审
3. ✅ 再次弹出反馈界面（第三轮）

**第三轮反馈**：选择"批准继续"

**预期效果**：
1. ✅ 对话继续，可能有更多轮次
2. ✅ 系统不会立即终止

**最终操作**：选择"结束对话"

**预期效果**：
1. ✅ 对话正式结束
2. ✅ 不再弹出反馈界面

### 测试用例3：不同反馈类型验证
**测试各种反馈类型的处理**：

1. **批准继续**：
   - 后端收到：`{content: "APPROVE", feedback_type: "approve"}`
   - 返回：`"用户批准: APPROVE"`
   - 对话继续

2. **需要修改**：
   - 后端收到：`{content: "请增加性能测试", feedback_type: "modify"}`
   - 返回：`"用户要求修改: 请增加性能测试，请根据反馈进行改进"`
   - 对话继续

3. **拒绝方案**：
   - 后端收到：`{content: "方案不可行", feedback_type: "reject"}`
   - 返回：`"用户拒绝: 方案不可行，请重新生成方案"`
   - 对话继续

4. **结束对话**：
   - 后端收到：`{content: "TERMINATE", feedback_type: "approve"}`
   - 返回：`"TERMINATE"`
   - 对话结束

## 技术验证点

### 前端检测逻辑
```typescript
// 检查是否有任何智能体正在流式输出
const hasAnyStreaming = messages.some(msg => msg.isStreaming)

// 检查Critic是否完成
const hasCriticStreaming = messages.some(msg => 
  msg.isStreaming && msg.agentName === 'critic'
)

// 确保前端输出完全停止
if (isGenerating) {
  console.log('⏳ 前端仍在输出，延迟显示反馈弹窗')
  return
}
```

### 后端循环机制
```python
# 终止条件配置
text_termination = TextMentionTermination("TERMINATE")

# 团队配置
team = RoundRobinGroupChat(
    agents,
    termination_condition=text_termination,
    max_turns=20,  # 允许最多20轮对话
)

# 反馈处理逻辑
if feedback_type == "approve":
    if content.upper() == "APPROVE":
        return "TERMINATE"  # 明确批准时终止
    else:
        return f"用户批准: {content}"  # 继续对话
```

## 调试信息

### 关键日志序列
```
# Critic完成输出
📝 critic 智能体完成，内容长度: 1234 字符

# 前端检测
⏳ 智能体仍在输出，延迟显示反馈弹窗
✅ Critic智能体输出完成，显示反馈弹窗

# 后端反馈处理
🔔 UserProxyAgent 等待用户反馈
📝 收到用户反馈: 需要修改...
📋 反馈类型: modify

# 循环继续
🚀 启动对话 conv_xxx 的团队任务
```

## 预期改进效果

### 用户体验
- **时机准确**：只在Critic完成后弹出反馈界面
- **循环反馈**：支持多轮改进和优化
- **明确控制**：用户可以选择继续或结束对话
- **操作灵活**：提供多种反馈类型选择

### 系统稳定性
- **状态管理**：精确的智能体状态检测
- **循环控制**：防止无限循环，最多20轮
- **错误处理**：异常情况下的优雅降级
- **资源管理**：及时清理团队实例

开始测试循环反馈机制！
