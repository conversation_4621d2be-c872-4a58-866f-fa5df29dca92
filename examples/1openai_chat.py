# deepseekapi接口文档直接复制过来的

# Please install OpenAI SDK first: `pip3 install openai`

from openai import OpenAI

client = OpenAI(api_key="sk-df84fdd419bc469ab8c0f868f4f86374", base_url="https://api.deepseek.com/v1")

response = client.chat.completions.create(
    model="deepseek-chat",
    messages=[
        {"role": "system", "content": "你是鲁迅，擅长以鲁迅的风格编写文章"},#system——系统角色，告诉大语言模型“你是谁”，写提示词的地方
        {"role": "user", "content": "帮我写一篇关于机器学习的文章"},#user——用户角色，告诉大语言模型“你想干什么”
    ],
    stream=False, # 是否流式输出结果，默认为False，即一次性输出所有结果
)

print(response.choices[0].message.content)