# autogen，quick start中直接复制过来的
# agent = AssistantAgent(
#     name="weather_agent",
#     model_client=model_client,
#     tools=[get_weather],
#     system_message="You are a helpful assistant.",
#     reflect_on_tool_use=True,
#     model_client_stream=True,  # Enable streaming tokens from the model client.
# )
###这是输出
# async def main() -> None:
#     await Console(agent.run_stream(task="What is the weather in New York?"))
#     # Close the connection to the model client.
#     await model_client.close()
#
#
# # NOTE: if running this inside a Python script you'll need to use asyncio.run(main()).
# await main()
###改造如下

import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent
from autogen_agentchat.ui import Console

from llms import model_client


agent = AssistantAgent(
    name="reporter_agent", #用英文名称
    model_client=model_client,
    system_message="你擅长编写古诗",
    model_client_stream=True,   # 支持流式输出
)
# await 不能直接写在模块中
# 如果函数中调用了协程函数，那么当前函数必须声明为协程函数，run是一个协程函数，所以需要await
async def main():
    result = await agent.run(task="编写一首4言古诗")   # 等待run方法执行完成后返回结果
    print(result)
# asyncio.run(main())

async def main_stream(): #流式输出
    # 获取协程对象
    result = agent.run_stream(task="编写一首4言古诗")  # 当前代码不会执行run_stream()中的代码,只会直接返回协程对象
    async for item in result: #遍历协程对象
        # print(item)
        if isinstance(item, ModelClientStreamingChunkEvent):
            print(item.content, end="", flush=True) #   打印时不换行 (end=""),强制立即显示 (flush=True)
            '''
            end=""
            默认情况下，print() 每次打印完会自动换行（就像打字机打完一行“咔嗒”一声跳到下一行）。
            这里设置 end="" 表示不要换行，让字紧接着上一个字继续打（像真正的打字机一样连续输出）。
            flush=True
            正常情况下，程序会偷偷“攒一会儿”再显示内容（比如等一句话打完再显示），但设置 flush=True 就是命令它：别攒了！立刻给我显示！
            '''
asyncio.run(main_stream())

async def main_console():
    await Console(agent.run_stream(task="编写一首4言古诗"))
# asyncio.run(main_console())
