# 滚动条跟随和用户反馈弹窗测试

## 测试目标
验证以下两个关键功能：
1. 流式输出时滚动条始终跟随到底部
2. 用户反馈弹窗能够正确弹出

## 测试用例

### 测试用例1：滚动条实时跟随
**输入消息**："请帮我设计一个用户登录功能的详细测试用例，包括正常流程、异常处理、边界条件等"

**预期效果**：
- ✅ AI开始生成内容时，滚动条立即跟随
- ✅ 每次内容更新，滚动条都滚动到最底部
- ✅ 用户能实时看到最新的输出内容
- ✅ 不会出现内容已生成但滚动条停留在中间的情况

**验证方法**：
1. 发送测试消息
2. 观察滚动条是否始终在底部
3. 检查是否能看到最新的输出内容

### 测试用例2：用户反馈弹窗
**输入消息**："请设计测试用例并等待我的确认"

**预期效果**：
- ✅ Primary智能体生成测试用例
- ✅ Critic智能体进行评审
- ✅ UserProxyAgent请求用户反馈
- ✅ 前端自动弹出反馈窗口
- ✅ 显示AI的具体提示信息

**验证方法**：
1. 发送测试消息
2. 等待AI完成生成
3. 观察是否弹出用户反馈窗口
4. 检查弹窗内容是否正确

### 测试用例3：综合测试
**输入消息**："请分析这个API接口的安全性并提供改进建议，完成后等待我的反馈"

**预期效果**：
- ✅ 流式输出过程中滚动条正常跟随
- ✅ 多智能体协作正常工作
- ✅ 输出完成后弹出用户反馈窗口
- ✅ 用户可以选择批准、修改或拒绝

## 调试信息

### 后端日志关键信息
```
🔔 UserProxyAgent 等待用户反馈
📋 提示信息: [具体提示]
🚩 设置反馈状态: waiting_for_feedback = True
⏳ 开始等待用户反馈，超时时间: 300秒
```

### 前端日志关键信息
```
📜 流式输出中，强制滚动到底部
🔄 开始定期检查用户反馈状态
🔍 反馈状态检查: {is_waiting_feedback: true, ...}
🔔 显示用户反馈弹窗: [提示信息]
```

## 问题排查

### 如果滚动条不跟随：
1. 检查控制台是否有"📜 流式输出中，强制滚动到底部"日志
2. 确认hasStreamingMessage状态是否正确
3. 检查scrollToBottom函数是否被调用

### 如果反馈弹窗不出现：
1. 检查后端是否有UserProxyAgent相关日志
2. 确认waiting_for_feedback状态是否为true
3. 检查前端反馈状态检查是否正常运行
4. 验证API接口/chat/feedback/status是否返回正确数据

开始测试！
