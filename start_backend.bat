@echo off
echo ========================================
echo    启动 Autogen Chat 后端服务
echo ========================================
echo.

cd backend

echo 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python，请确保已安装 Python 3.8+
    pause
    exit /b 1
)

echo.
echo 检查依赖包...
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 检查环境变量文件...
if not exist .env (
    echo 错误: 未找到 .env 文件，请先配置环境变量
    echo 请复制 .env.example 为 .env 并填入正确的配置
    pause
    exit /b 1
)

echo.
echo 🚀 启动后端服务...
echo 服务地址: http://localhost:8000
echo API 文档: http://localhost:8000/docs
echo.
echo 按 Ctrl+C 停止服务
echo.

python main.py

pause
