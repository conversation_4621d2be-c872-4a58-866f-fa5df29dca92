# Autogen Chat - 智能对话系统

🚀 基于 Microsoft Autogen 框架构建的炫酷智能对话系统，具有科技感十足的界面设计和流式对话体验。

## ✨ 主要特性

- 🤖 **智能对话**: 基于 Autogen 0.5.7 框架，支持多轮对话
- 🌊 **流式输出**: 实时流式响应，类似 ChatGPT 的打字机效果
- 🎨 **炫酷界面**: 科技感十足的 UI 设计，参考 Gemini 风格
- 📱 **响应式设计**: 完美适配桌面端和移动端
- 🎯 **Markdown 支持**: 支持代码高亮、表格、列表等格式
- ⚡ **高性能**: 基于 React + FastAPI 的现代技术栈

## 🛠️ 技术栈

### 前端
- **框架**: React 18 + TypeScript + Vite
- **UI 库**: Ant Design X + Ant Design 5
- **样式**: Tailwind CSS + 自定义 CSS
- **动画**: Framer Motion
- **其他**: React Markdown, Syntax Highlighter

### 后端
- **框架**: FastAPI
- **AI 引擎**: Autogen 0.5.7
- **流式协议**: Server-Sent Events (SSE)
- **数据验证**: Pydantic

## 📦 安装和运行

### 环境要求
- Python 3.8+
- Node.js 16+
- npm 或 yarn

### 1. 克隆项目
```bash
git clone <repository-url>
cd autogen-chat
```

### 2. 后端设置
```bash
# 进入后端目录
cd backend

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置你的 API 密钥

# 启动后端服务
python main.py
```

后端服务将在 `http://localhost:8000` 启动

### 3. 前端设置
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:3000` 启动

## 🔧 配置说明

### 后端配置 (backend/.env)
```env
MODEL=deepseek-chat                    # 使用的模型名称
BASE_URL=https://api.deepseek.com/v1   # API 基础 URL
API_KEY=your-api-key-here              # 你的 API 密钥
```

### 前端配置 (frontend/.env)
```env
VITE_API_URL=http://localhost:8000     # 后端 API 地址
VITE_APP_TITLE=Autogen Chat           # 应用标题
VITE_APP_VERSION=1.0.0                # 应用版本
```

## 🚀 部署

### 后端部署
```bash
# 使用 uvicorn 部署
uvicorn main:app --host 0.0.0.0 --port 8000

# 或使用 gunicorn (推荐生产环境)
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### 前端部署
```bash
# 构建生产版本
npm run build

# 部署 dist 目录到你的 Web 服务器
```

## 📖 API 文档

启动后端服务后，访问以下地址查看 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 主要接口

#### 1. 流式聊天
```
POST /chat/stream
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己",
  "conversation_id": "conv_123456",
  "user_id": "user_789"
}
```

#### 2. 普通聊天
```
POST /chat
Content-Type: application/json

{
  "message": "你好，请介绍一下你自己"
}
```

#### 3. 健康检查
```
GET /health
```

## 🎨 界面特性

### 科技感设计元素
- 🌈 **渐变背景**: 深色主题配合霓虹色渐变
- ✨ **粒子效果**: 动态粒子背景动画
- 🔮 **玻璃态效果**: 半透明毛玻璃质感
- 💫 **光晕动画**: 霓虹光效和脉冲动画
- 🎯 **网格背景**: 科技感网格纹理

### 交互体验
- 📝 **流式打字**: AI 回复的实时打字机效果
- 🎪 **平滑动画**: 消息出现的流畅过渡动画
- 📱 **响应式布局**: 自适应不同屏幕尺寸
- ⌨️ **快捷键支持**: Enter 发送，Shift+Enter 换行
- 🎨 **Markdown 渲染**: 支持代码高亮和格式化

## 🔍 功能说明

### 聊天功能
- ✅ 多轮对话支持
- ✅ 消息历史记录
- ✅ 流式响应显示
- ✅ 消息复制功能
- ✅ 对话清空功能

### AI 能力
- 🧠 **智能问答**: 回答各种问题
- 📝 **文本创作**: 写作、翻译、总结
- 💻 **代码助手**: 编程问题解答
- 🎨 **创意支持**: 创意写作和头脑风暴
- 📚 **学习辅导**: 知识解释和教学

## 🐛 故障排除

### 常见问题

1. **后端启动失败**
   - 检查 Python 版本是否 >= 3.8
   - 确认已安装所有依赖: `pip install -r requirements.txt`
   - 检查 .env 文件配置是否正确

2. **前端无法连接后端**
   - 确认后端服务已启动
   - 检查前端 .env 文件中的 API_URL 配置
   - 查看浏览器控制台是否有 CORS 错误

3. **流式响应不工作**
   - 检查浏览器是否支持 EventSource
   - 确认网络连接稳定
   - 查看后端日志是否有错误

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至: [<EMAIL>]

---

⭐ 如果这个项目对你有帮助，请给个 Star！


AI_test_project_2/
├── backend/           # FastAPI后端
├── frontend/          # React前端  
├── examples/          # 原有示例
├── README.md          # 详细说明
├── start_all.bat      # 一键启动
├── fix_issues.py      # 问题修复
└── test_system.py     # 系统测试
