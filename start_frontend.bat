@echo off
echo ========================================
echo    启动 Autogen Chat 前端应用
echo ========================================
echo.

cd frontend

echo 检查 Node.js 环境...
node --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Node.js，请确保已安装 Node.js 16+
    pause
    exit /b 1
)

echo.
echo 检查 npm...
npm --version
if %errorlevel% neq 0 (
    echo 错误: 未找到 npm
    pause
    exit /b 1
)

echo.
echo 检查依赖包...
if not exist node_modules (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 🚀 启动前端开发服务器...
echo 应用地址: http://localhost:3000
echo.
echo 按 Ctrl+C 停止服务
echo.

npm run dev

pause
