// 测试附件内容提取API的简单脚本
// 可以在浏览器控制台中运行

async function testAttachmentExtraction() {
  try {
    // 1. 首先上传测试文件
    console.log('📤 正在上传测试文件...');
    
    const formData = new FormData();
    const testContent = `# 测试文件
这是一个测试文件，用于验证附件内容提取功能。

## 内容
- 项目1
- 项目2
- 项目3

测试完成！`;
    
    const blob = new Blob([testContent], { type: 'text/markdown' });
    formData.append('file', blob, 'test.md');
    
    const uploadResponse = await fetch('http://localhost:8000/files/upload', {
      method: 'POST',
      body: formData
    });
    
    if (!uploadResponse.ok) {
      throw new Error('文件上传失败');
    }
    
    const uploadResult = await uploadResponse.json();
    console.log('✅ 文件上传成功:', uploadResult);
    
    // 2. 测试内容提取
    console.log('📖 正在提取文件内容...');
    
    const contentResponse = await fetch(`http://localhost:8000/files/${uploadResult.file_id}/content`);
    
    if (!contentResponse.ok) {
      throw new Error('内容提取失败');
    }
    
    const contentResult = await contentResponse.json();
    console.log('✅ 内容提取成功:', contentResult);
    
    // 3. 模拟前端附件内容提取逻辑
    console.log('🔄 模拟前端内容处理...');
    
    const attachments = [uploadResult];
    let attachmentContent = '';
    
    for (const attachment of attachments) {
      const response = await fetch(`http://localhost:8000/files/${attachment.file_id}/content`);
      const contentData = await response.json();
      
      let fileContent = '';
      switch (contentData.type) {
        case 'text':
        case 'markdown':
          fileContent = typeof contentData.content === 'string' 
            ? contentData.content 
            : contentData.content.markdown || contentData.content.text || '';
          break;
        default:
          fileContent = `[${contentData.type}文件: ${attachment.original_name}]`;
      }
      
      attachmentContent += `--- 附件: ${attachment.original_name} ---\n${fileContent}`;
    }
    
    console.log('📎 提取的附件内容:');
    console.log(attachmentContent);
    
    // 4. 模拟合并用户输入和附件内容
    const userInput = '请分析这个文件的内容';
    const combinedContent = `${userInput}\n\n${attachmentContent}`;
    
    console.log('📝 合并后的完整内容:');
    console.log(combinedContent);
    
    console.log('🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testAttachmentExtraction();
