<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>XingHe Intelligence - 智能对话助手</title>
    <meta name="description" content="基于Autogen的智能对话系统，炫酷科技感界面" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
      /* 全局样式重置 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: rgba(15, 23, 42, 0.5);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #00d4ff, #8b5cf6);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(45deg, #0ea5e9, #a855f7);
      }
      
      /* 页面加载动画 */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 3px solid rgba(0, 212, 255, 0.3);
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 背景粒子效果 */
      .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: -1;
      }
      
      .particle {
        position: absolute;
        width: 2px;
        height: 2px;
        background: #00d4ff;
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
        opacity: 0.6;
      }
      
      /* 响应式设计 */
      @media (max-width: 768px) {
        html {
          font-size: 14px;
        }
      }
      
      @media (max-width: 480px) {
        html {
          font-size: 12px;
        }
      }
    </style>
  </head>
  <body>
    <!-- 页面加载动画 -->
    <div id="loading-screen" class="loading-screen">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 背景粒子效果 -->
    <div class="particles" id="particles"></div>
    
    <!-- React 应用挂载点 -->
    <div id="root"></div>
    
    <!-- 粒子效果脚本 -->
    <script>
      // 创建背景粒子效果
      function createParticles() {
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;
        
        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.className = 'particle';
          particle.style.left = Math.random() * 100 + '%';
          particle.style.top = Math.random() * 100 + '%';
          particle.style.animationDelay = Math.random() * 6 + 's';
          particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
          particlesContainer.appendChild(particle);
        }
      }
      
      // 页面加载完成后隐藏加载动画
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
      
      // 创建粒子效果
      createParticles();
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
