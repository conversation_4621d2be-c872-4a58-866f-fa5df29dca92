/**
 * 聊天服务
 * 处理与后端API的通信，包括普通请求和SSE流式请求
 */

import axios, { AxiosInstance } from 'axios'

// 消息接口定义
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  isStreaming?: boolean
  agentName?: string  // 智能体名称
  attachments?: any[]  // 附件列表
}

// 聊天请求接口
export interface ChatRequest {
  message: string
  conversation_id?: string
  user_id?: string
  attachments?: any[]  // 附件列表
}

// 聊天响应接口
export interface ChatResponse {
  message: string
  status: string
  conversation_id?: string
  timestamp: string
}

// 流式数据块接口
export interface StreamChunk {
  type: 'start' | 'chunk' | 'end' | 'error' | 'message_start' | 'message_end' | 'user_proxy_request'
  content: string
  timestamp?: string
  agentName?: string  // 智能体名称
}

// 对话接口
export interface Conversation {
  id: string
  title: string
  created_at: number
  updated_at: number
  message_count: number
  user_message_count?: number // 用户消息数量
  has_user_messages?: boolean // 是否有用户消息
}

/**
 * 聊天服务类
 * 封装与后端API的所有通信逻辑
 */
export class ChatService {
  private api: AxiosInstance
  private baseURL: string

  constructor() {
    // 根据环境设置API基础URL
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000'
    
    // 创建axios实例
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000, // 30秒超时
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        console.log('🚀 发送请求:', config.method?.toUpperCase(), config.url)
        return config
      },
      (error) => {
        console.error('❌ 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.api.interceptors.response.use(
      (response) => {
        console.log('✅ 收到响应:', response.status, response.config.url)
        return response
      },
      (error) => {
        console.error('❌ 响应错误:', error.response?.status, error.message)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 发送普通聊天消息（非流式）
   */
  async sendMessage(message: string, conversationId?: string): Promise<ChatResponse> {
    try {
      const request: ChatRequest = {
        message,
        conversation_id: conversationId,
        user_id: 'user_' + Date.now(),
      }

      const response = await this.api.post<ChatResponse>('/chat', request)
      return response.data
    } catch (error) {
      console.error('发送消息失败:', error)
      throw new Error('发送消息失败，请检查网络连接')
    }
  }

  /**
   * 发送流式聊天消息（SSE）
   */
  async sendStreamMessage(
    message: string,
    attachments: any[] | undefined,
    onChunk: (chunk: string, agentName?: string) => void,
    onComplete: () => void,
    onError: (error: string) => void,
    onAgentChange?: (agentName: string) => void,
    onUserProxyRequest?: (prompt: string, agentName?: string) => void,
    conversationId?: string
  ): Promise<void> {
    const request: ChatRequest = {
      message,
      conversation_id: conversationId,
      user_id: 'user_' + Date.now(),
      attachments: attachments || [],
    }

    await this.handleStreamResponse(
      `${this.baseURL}/chat/stream`,
      'POST',
      request,
      onChunk,
      onComplete,
      onError,
      onAgentChange,
      onUserProxyRequest
    )
  }

  /**
   * 通用流式响应处理方法
   */
  private async handleStreamResponse(
    url: string,
    method: 'GET' | 'POST',
    data?: any,
    onChunk?: (chunk: string, agentName?: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void,
    onAgentChange?: (agentName: string) => void,
    onUserProxyRequest?: (prompt: string, agentName?: string) => void
  ): Promise<void> {
    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
      }

      if (method === 'POST' && data) {
        options.body = JSON.stringify(data)
      }

      const response = await fetch(url, options)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()

        if (done) {
          onComplete?.()
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: StreamChunk = JSON.parse(line.slice(6))
              this.processStreamChunk(data, onChunk, onComplete, onError, onAgentChange, onUserProxyRequest)
            } catch (parseError) {
              console.warn('⚠️ 跳过无效的SSE数据:', line)
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ 流式请求失败:', error)
      onError?.(error instanceof Error ? error.message : '请求失败')
    }
  }

  /**
   * 处理流式数据块
   */
  private processStreamChunk(
    data: StreamChunk,
    onChunk?: (chunk: string, agentName?: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void,
    onAgentChange?: (agentName: string) => void,
    onUserProxyRequest?: (prompt: string, agentName?: string) => void
  ): void {
    switch (data.type) {
      case 'chunk':
        if (data.content && onChunk) {
          onChunk(data.content, data.agentName)
          if (data.agentName && onAgentChange) {
            onAgentChange(data.agentName)
          }
        }
        break
      case 'message_start':
        if (onChunk) {
          onChunk('__NEW_MESSAGE__', data.agentName)
          if (data.agentName && onAgentChange) {
            onAgentChange(data.agentName)
          }
        }
        break
      case 'user_proxy_request':
        // 处理用户反馈请求
        console.log('🔔 收到用户反馈请求:', data.content)
        if (onUserProxyRequest) {
          onUserProxyRequest(data.content, data.agentName)
        }
        break
      case 'error':
        onError?.(data.content)
        break
      case 'end':
        onComplete?.()
        break
      default:
        console.log(`📝 处理流式数据: ${data.type}`)
    }
  }

  /**
   * 使用EventSource的流式请求（备用方案，需要GET接口）
   */
  async sendStreamMessageWithEventSource(
    message: string,
    onChunk: (chunk: string) => void,
    onComplete: () => void,
    onError: (error: string) => void,
    conversationId?: string
  ): Promise<void> {
    try {
      const request: ChatRequest = {
        message,
        conversation_id: conversationId,
        user_id: 'user_' + Date.now(),
      }

      // 创建EventSource连接
      const eventSource = new EventSource(
        `${this.baseURL}/chat/stream?${new URLSearchParams({
          message: request.message,
          conversation_id: request.conversation_id || '',
          user_id: request.user_id || '',
        })}`
      )

      // 处理消息事件
      eventSource.onmessage = (event) => {
        try {
          const data: StreamChunk = JSON.parse(event.data)
          
          switch (data.type) {
            case 'start':
              console.log('🎬 开始接收流式数据')
              break
              
            case 'chunk':
              if (data.content) {
                onChunk(data.content)
              }
              break
              
            case 'end':
              console.log('🏁 流式数据接收完成')
              eventSource.close()
              onComplete()
              break
              
            case 'error':
              console.error('❌ 服务器错误:', data.content)
              eventSource.close()
              onError(data.content)
              break
              
            default:
              console.warn('⚠️ 未知的数据类型:', data.type)
          }
        } catch (parseError) {
          console.error('❌ 解析SSE数据失败:', parseError)
          onError('数据解析失败')
        }
      }

      // 处理连接错误
      eventSource.onerror = (error) => {
        console.error('❌ SSE连接错误:', error)
        eventSource.close()
        onError('连接服务器失败，请检查网络')
      }

      // 处理连接打开
      eventSource.onopen = () => {
        console.log('🔗 SSE连接已建立')
      }

    } catch (error) {
      console.error('❌ 创建SSE连接失败:', error)
      onError('无法建立连接，请重试')
    }
  }

  /**
   * 使用fetch API发送流式请求（备用方案）
   */
  async sendStreamMessageWithFetch(
    message: string,
    onChunk: (chunk: string, agentName?: string) => void,
    onComplete: () => void,
    onError: (error: string) => void,
    onAgentChange?: (agentName: string) => void,
    conversationId?: string
  ): Promise<void> {
    try {
      const request: ChatRequest = {
        message,
        conversation_id: conversationId,
        user_id: 'user_' + Date.now(),
      }

      const response = await fetch(`${this.baseURL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify(request),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          onComplete()
          break
        }

        // 解码数据块
        buffer += decoder.decode(value, { stream: true })
        
        // 处理SSE数据
        const lines = buffer.split('\n')
        buffer = lines.pop() || '' // 保留不完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: StreamChunk = JSON.parse(line.slice(6))

              if (data.type === 'chunk' && data.content) {
                onChunk(data.content, data.agentName)
                // 如果智能体发生变化，通知前端
                if (data.agentName && onAgentChange) {
                  onAgentChange(data.agentName)
                }
              } else if (data.type === 'message_start') {
                // 处理新消息开始信号 - 创建新的消息气泡
                onChunk('__NEW_MESSAGE__', data.agentName)
                if (data.agentName && onAgentChange) {
                  onAgentChange(data.agentName)
                }
              } else if (data.type === 'message_end') {
                // 处理消息结束信号
                console.log(`🔚 消息结束: ${data.agentName}`)
              } else if (data.type === 'error') {
                onError(data.content)
                return
              } else if (data.type === 'end') {
                onComplete()
                return
              }
            } catch (parseError) {
              console.warn('⚠️ 跳过无效的SSE数据:', line)
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ Fetch流式请求失败:', error)
      onError(error instanceof Error ? error.message : '请求失败')
    }
  }

  /**
   * 检查服务器健康状态
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.api.get('/health')
      return response.data.status === 'healthy'
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      return false
    }
  }

  /**
   * 获取API信息
   */
  async getApiInfo(): Promise<any> {
    try {
      const response = await this.api.get('/')
      return response.data
    } catch (error) {
      console.error('❌ 获取API信息失败:', error)
      throw error
    }
  }

  // 对话管理方法
  /**
   * 获取对话列表
   * @param includeEmpty 是否包含没有用户消息的对话，默认为false
   */
  async getConversations(includeEmpty: boolean = false): Promise<Conversation[]> {
    try {
      const response = await this.api.get('/conversations', {
        params: { include_empty: includeEmpty }
      })
      return response.data.conversations
    } catch (error) {
      console.error('❌ 获取对话列表失败:', error)
      throw error
    }
  }

  /**
   * 创建新对话
   */
  async createConversation(title: string = '新对话'): Promise<string> {
    try {
      const response = await this.api.post('/conversations', null, {
        params: { title }
      })
      return response.data.conversation_id
    } catch (error) {
      console.error('❌ 创建对话失败:', error)
      throw error
    }
  }

  /**
   * 获取指定对话
   */
  async getConversation(conversationId: string): Promise<any> {
    try {
      const response = await this.api.get(`/conversations/${conversationId}`)
      return response.data
    } catch (error) {
      console.error('❌ 获取对话失败:', error)
      throw error
    }
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      await this.api.delete(`/conversations/${conversationId}`)
      return true
    } catch (error) {
      console.error('❌ 删除对话失败:', error)
      return false
    }
  }

  /**
   * 删除所有对话
   */
  async deleteAllConversations(): Promise<{ success: boolean; count: number }> {
    try {
      const response = await this.api.delete('/conversations')
      console.log('✅ 删除所有对话成功:', response.data.message)
      return { success: true, count: 0 } // 后端会返回具体数量
    } catch (error) {
      console.error('❌ 删除所有对话失败:', error)
      return { success: false, count: 0 }
    }
  }

  // 生成控制方法
  /**
   * 停止生成
   */
  async stopGeneration(conversationId?: string): Promise<boolean> {
    try {
      const params = conversationId ? { conversation_id: conversationId } : {}
      await this.api.post('/chat/stop', null, { params })
      console.log(`✅ 停止生成成功 ${conversationId ? `(对话: ${conversationId})` : '(全部对话)'}`)
      return true
    } catch (error) {
      console.error('❌ 停止生成失败:', error)
      return false
    }
  }

  /**
   * 继续生成（恢复前端显示并获取缓存内容）
   */
  async continueGeneration(
    onChunk: (chunk: string, agentName?: string) => void,
    onComplete: () => void,
    onError: (error: string) => void,
    conversationId?: string
  ): Promise<void> {
    try {
      if (!conversationId) {
        throw new Error('缺少conversation_id参数')
      }

      const params = `?conversation_id=${conversationId}`
      const response = await fetch(`${this.baseURL}/chat/continue${params}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            console.log('📥 缓存内容接收完成')
            onComplete()
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))
                const { content, agent_name } = data

                if (content) {
                  onChunk(content, agent_name)
                }
              } catch (parseError) {
                console.warn('⚠️ 解析SSE数据失败:', parseError)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }
    } catch (error) {
      console.error('❌ 继续生成失败:', error)
      onError(error instanceof Error ? error.message : '继续生成失败')
    }
  }

  /**
   * 获取生成状态
   */
  async getGenerationStatus(conversationId?: string): Promise<boolean> {
    try {
      const params = conversationId ? { conversation_id: conversationId } : {}
      const response = await this.api.get('/chat/status', { params })
      return response.data.is_generating
    } catch (error) {
      console.error('❌ 获取生成状态失败:', error)
      return false
    }
  }
}
