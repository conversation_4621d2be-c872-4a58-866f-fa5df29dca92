/**
 * 用户反馈服务
 * 处理与后端的用户反馈交互
 */

interface UserFeedback {
  content: string
  feedback_type: 'approve' | 'modify' | 'reject' | 'general'
  conversation_id?: string
}

interface FeedbackResponse {
  message: string
  feedback_type: string
  conversation_id?: string
}

interface FeedbackStatus {
  queue_size: number
  is_waiting_feedback: boolean
  message: string
}

class FeedbackService {
  private baseUrl: string

  constructor() {
    this.baseUrl = 'http://localhost:8000'
  }

  /**
   * 提交用户反馈
   */
  async submitFeedback(feedback: UserFeedback): Promise<FeedbackResponse> {
    try {
      console.log('📤 提交用户反馈:', feedback)

      const response = await fetch(`${this.baseUrl}/chat/feedback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedback),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.detail || `HTTP ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ 反馈提交成功:', result)
      return result
    } catch (error) {
      console.error('❌ 提交反馈失败:', error)
      throw error
    }
  }

  /**
   * 获取反馈队列状态
   */
  async getFeedbackStatus(): Promise<FeedbackStatus> {
    try {
      const response = await fetch(`${this.baseUrl}/chat/feedback/status`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('❌ 获取反馈状态失败:', error)
      throw error
    }
  }

  /**
   * 快速批准
   */
  async quickApprove(conversationId?: string): Promise<FeedbackResponse> {
    return this.submitFeedback({
      content: 'APPROVE',
      feedback_type: 'approve',
      conversation_id: conversationId
    })
  }

  /**
   * 快速拒绝
   */
  async quickReject(reason: string, conversationId?: string): Promise<FeedbackResponse> {
    return this.submitFeedback({
      content: reason,
      feedback_type: 'reject',
      conversation_id: conversationId
    })
  }

  /**
   * 请求修改
   */
  async requestModification(modification: string, conversationId?: string): Promise<FeedbackResponse> {
    return this.submitFeedback({
      content: modification,
      feedback_type: 'modify',
      conversation_id: conversationId
    })
  }
}

// 创建单例实例
const feedbackService = new FeedbackService()

export default feedbackService
export type { UserFeedback, FeedbackResponse, FeedbackStatus }
