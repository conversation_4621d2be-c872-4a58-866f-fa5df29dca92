/**
 * 主应用组件
 * 包含整个聊天界面的布局和状态管理
 */

import { useState, useEffect } from 'react'
import { message } from 'antd'
import { motion, AnimatePresence } from 'framer-motion'
import ChatContainer from './components/ChatContainer'
import Header from './components/Header'
import Sidebar from './components/Sidebar'
import BackgroundEffects from './components/BackgroundEffects'
import UserFeedbackModal from './components/UserFeedbackModal'
import { ChatMessage, ChatService, Conversation } from './services/chatService'
import feedbackService, { UserFeedback } from './services/feedbackService'
import './App.css'



/**
 * 应用主组件
 */
function App() {
  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [currentConversationId, setCurrentConversationId] = useState<string>('')
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [canContinue, setCanContinue] = useState(false) // 是否可以继续生成
  const [isStopped, setIsStopped] = useState(false) // 是否被用户手动停止
  const [backendGenerating, setBackendGenerating] = useState(false) // 后端是否在生成

  // 用户反馈相关状态
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)
  const [feedbackPrompt, setFeedbackPrompt] = useState('')
  const [isWaitingFeedback, setIsWaitingFeedback] = useState(false)

  // 调试：监控关键状态变化
  useEffect(() => {
    console.log('🎯 canContinue 状态变化:', canContinue)
  }, [canContinue])

  useEffect(() => {
    console.log('🔄 isGenerating 状态变化:', isGenerating)
  }, [isGenerating])

  useEffect(() => {
    console.log('⚙️ backendGenerating 状态变化:', backendGenerating)
  }, [backendGenerating])

  useEffect(() => {
    console.log('🆔 currentConversationId 状态变化:', currentConversationId)
  }, [currentConversationId])

  // 聊天服务实例
  const chatService = new ChatService()

  // 检查是否为有效的智能体
  const isValidAgent = (agentName?: string): boolean => {
    return agentName === 'primary' || agentName === 'critic' || agentName === 'user_proxy' || agentName === 'optimizer' || agentName === 'security'
  }

  // 处理用户反馈提交
  const handleUserFeedback = async (feedback: UserFeedback) => {
    try {
      console.log('📝 提交用户反馈:', feedback)
      await feedbackService.submitFeedback(feedback)
      setShowFeedbackModal(false)
      setIsWaitingFeedback(false)

      // 根据反馈类型显示不同的提示
      if (feedback.feedback_type === 'approve' && feedback.content === 'TERMINATE') {
        message.success('对话已结束')
      } else if (feedback.feedback_type === 'approve') {
        message.success('意见已采纳')
      } else if (feedback.feedback_type === 'modify') {
        message.success('修改建议已提交')
      } else if (feedback.feedback_type === 'reject') {
        message.success('拒绝意见已提交')
      } else {
        message.success('反馈已提交')
      }

      // 提交反馈后，稍等片刻再检查状态（可能有新的反馈需求）
      setTimeout(() => {
        checkFeedbackStatus()
      }, 1000)
    } catch (error) {
      console.error('❌ 提交用户反馈失败:', error)
      message.error('提交反馈失败')
    }
  }

  // 检查是否需要用户反馈 - 增强等待逻辑
  const checkFeedbackStatus = async () => {
    try {
      const status = await feedbackService.getFeedbackStatus()
      console.log('🔍 反馈状态检查:', {
        is_waiting_feedback: status.is_waiting_feedback,
        feedback_prompt: status.feedback_prompt,
        showFeedbackModal,
        isGenerating,
        backendGenerating,
        messagesCount: messages.length
      })

      if (status.is_waiting_feedback && !showFeedbackModal) {
        console.log('📋 后端正在等待用户反馈')

        // 更严格的检测：确保所有输出都完成
        if (isGenerating || backendGenerating) {
          console.log('⏳ 系统仍在生成中，延迟显示反馈弹窗')
          return // 等待下次检查
        }

        // 检查是否有任何消息还在流式输出
        const hasStreamingMessage = messages.some(msg => msg.isStreaming)
        if (hasStreamingMessage) {
          console.log('⏳ 仍有消息在流式输出，延迟显示反馈弹窗')
          return // 等待下次检查
        }

        // 额外等待确保所有输出完成
        console.log('⏱️ 等待2秒确保输出完全结束')
        setTimeout(() => {
          // 再次检查状态
          if (!isGenerating && !backendGenerating && !messages.some(msg => msg.isStreaming)) {
            console.log('✅ 确认输出完成，显示用户反馈弹窗')
            setIsWaitingFeedback(true)
            setFeedbackPrompt(status.feedback_prompt || 'AI智能体正在等待您的反馈')
            setShowFeedbackModal(true)
            console.log('🔔 用户反馈弹窗已显示:', status.feedback_prompt)
          }
        }, 2000)

      } else if (!status.is_waiting_feedback && showFeedbackModal) {
        // 如果不再需要反馈，关闭弹窗
        setShowFeedbackModal(false)
        setIsWaitingFeedback(false)
        console.log('✅ 关闭用户反馈弹窗')
      } else {
        console.log('📝 反馈状态:', {
          waiting: status.is_waiting_feedback,
          modalShown: showFeedbackModal,
          reason: !status.is_waiting_feedback ? '后端不在等待反馈' : '弹窗已显示'
        })
      }
    } catch (error) {
      console.error('❌ 检查反馈状态失败:', error)
    }
  }

  // 加载对话列表（只加载有用户消息的对话）
  const loadConversations = async () => {
    try {
      const convs = await chatService.getConversations(false) // 不包含空对话
      setConversations(convs)
      console.log(`📋 加载了 ${convs.length} 个有效对话`)
    } catch (error) {
      console.error('加载对话列表失败:', error)
    }
  }

  // 判断是否处于新对话状态（只有欢迎消息或无消息，用户未发送过消息）
  const isInNewConversation = () => {
    // 如果没有消息，或者只有一条欢迎消息（id为'welcome'），则认为是新对话
    // 检查是否有用户消息，如果有用户消息则不是新对话
    const hasUserMessage = messages.some(msg => msg.role === 'user')
    return !hasUserMessage && (messages.length === 0 ||
           (messages.length === 1 && messages[0].id === 'welcome'))
  }

  // 重置生成状态的公共函数
  const resetGenerationState = () => {
    setIsGenerating(false)
    setIsLoading(false)
    setBackendGenerating(false)
    setCanContinue(false)
    setIsStopped(false)
  }

  // 停止当前生成任务
  const stopCurrentGeneration = async () => {
    if (isGenerating || backendGenerating) {
      try {
        await chatService.stopGeneration(currentConversationId)
        resetGenerationState()
        console.log(`🛑 已停止对话 ${currentConversationId} 的生成任务`)
      } catch (error) {
        console.error('停止生成任务失败:', error)
      }
    }
  }

  // 创建新对话
  const handleNewConversation = async () => {
    // 如果当前已经是新对话状态，不允许创建
    if (isInNewConversation()) {
      // 不显示提示信息，因为Sidebar中的tooltip已经提供了提示
      return
    }

    // 停止当前的生成任务
    await stopCurrentGeneration()

    try {
      const conversationId = await chatService.createConversation()

      // 完全重置所有状态，确保新对话独立，不携带任何历史
      setCurrentConversationId(conversationId)
      setMessages([])
      setIsGenerating(false)
      setBackendGenerating(false)
      setCanContinue(false)
      setIsStopped(false)
      setShowFeedbackModal(false)
      setIsWaitingFeedback(false)
      setFeedbackPrompt('')

      console.log('🆕 创建新对话，完全重置状态（无历史记录）:', conversationId)

      await loadConversations()
      message.success('新对话已创建')
    } catch (error) {
      message.error('创建对话失败')
    }
  }

  // 切换对话
  const handleSwitchConversation = async (conversationId: string) => {
    // 如果切换到当前对话，不需要操作
    if (conversationId === currentConversationId) {
      return
    }

    // 停止当前的生成任务
    await stopCurrentGeneration()

    try {
      const conversation = await chatService.getConversation(conversationId)
      setCurrentConversationId(conversationId)

      // 转换消息格式
      const chatMessages: ChatMessage[] = conversation.messages.map((msg: any) => ({
        id: msg.id,
        content: msg.content,
        role: msg.role,
        timestamp: new Date(msg.timestamp * 1000),
        // 只有assistant角色的消息才设置agentName，避免用户消息被错误标记
        ...(msg.role === 'assistant' && msg.agent_name ? { agentName: msg.agent_name } : {})
      }))

      setMessages(chatMessages)
      console.log(`🔄 已切换到对话: ${conversationId}`)
    } catch (error) {
      message.error('切换对话失败')
    }
  }

  // 删除对话
  const handleDeleteConversation = async (conversationId: string) => {
    try {
      await chatService.deleteConversation(conversationId)
      if (conversationId === currentConversationId) {
        setMessages([])
        setCurrentConversationId('')
      }
      await loadConversations()
      message.success('对话已删除')
    } catch (error) {
      message.error('删除对话失败')
    }
  }

  // 停止生成（暂停前端显示）
  const handleStopGeneration = async () => {
    try {
      setIsStopped(true) // 设置停止标志
      // 只暂停前端显示，不调用后端API
      setIsGenerating(false) // 前端停止生成状态
      setIsLoading(false) // 前端停止加载状态
      setCanContinue(true) // 设置可以继续生成
      // 注意：不设置 setBackendGenerating(false)，因为后端还在生成
      console.log(`🔴 暂停对话 ${currentConversationId} 前端显示，后端继续生成`)
      console.log(`🎯 设置 canContinue = true`)
      message.info('已暂停显示')
    } catch (error) {
      message.error('暂停显示失败')
    }
  }

  // 继续生成
  const handleContinueGeneration = async () => {
    try {
      if (!currentConversationId) {
        message.error('没有活动的对话，无法继续生成')
        return
      }

      console.log('🟢 恢复前端显示（获取缓存内容）')
      console.log(`🔍 当前对话ID: ${currentConversationId}`)
      setCanContinue(false) // 隐藏继续按钮
      setIsLoading(true) // 显示加载状态
      setIsGenerating(true) // 恢复前端生成状态，显示停止按钮

      // 调用继续生成API获取缓存内容
      await chatService.continueGeneration(
        // 流式数据回调
        (chunk: string, agentName?: string) => {
          setMessages(prev => {
            // 查找当前智能体的流式消息
            let targetMessage = prev.find(msg =>
              msg.isStreaming && msg.agentName === agentName
            )

            // 如果没有找到对应智能体的消息，创建一个新的
            if (!targetMessage) {
              const newMessage: ChatMessage = {
                id: `ai_${Date.now()}_${agentName || 'primary'}`,
                content: chunk,
                role: 'assistant',
                timestamp: new Date(),
                isStreaming: true,
                agentName: agentName || 'primary'
              }

              return prev.map(msg =>
                msg.isStreaming ? { ...msg, isStreaming: false } : msg
              ).concat(newMessage)
            }

            // 添加内容到现有消息
            return prev.map(msg =>
              msg.id === targetMessage.id
                ? { ...msg, content: msg.content + chunk }
                : msg
            )
          })
        },
        // 完成回调
        () => {
          setMessages(prev => prev.map(msg =>
            msg.isStreaming ? { ...msg, isStreaming: false } : msg
          ))
          setIsLoading(false)

          // 检查后端是否还在生成
          checkBackendStatus()

          console.log('✅ 缓存内容接收完成，检查后端状态')
        },
        // 错误回调
        (error: string) => {
          setIsLoading(false)
          setIsGenerating(false) // 错误时停止生成状态
          setCanContinue(true) // 错误时重新显示继续按钮
          message.error(`继续生成失败：${error}`)
        },
        currentConversationId
      )
    } catch (error) {
      console.error('继续生成错误:', error)
      setIsLoading(false)
      setIsGenerating(false) // 错误时停止生成状态
      setCanContinue(true) // 错误时重新显示继续按钮
      message.error('继续生成失败，请检查网络连接')
    }
  }

  // 检查后端生成状态
  const checkBackendStatus = async () => {
    try {
      const isGenerating = await chatService.getGenerationStatus(currentConversationId)
      console.log(`🔍 检查后端状态: isGenerating=${isGenerating}, 当前canContinue=${canContinue}`)

      if (!isGenerating) {
        // 后端已完成生成
        console.log(`🏁 对话 ${currentConversationId} 后端生成完成，更新UI状态`)
        setBackendGenerating(false)
        setIsGenerating(false)
        setIsLoading(false)
        setCanContinue(false) // 后端完成时才隐藏继续按钮
      } else {
        // 后端还在生成，但不要改变当前的canContinue状态
        console.log(`⏳ 对话 ${currentConversationId} 后端仍在生成，保持当前状态`)
        setBackendGenerating(true)
        // 不改变 isGenerating 和 canContinue 状态，保持用户当前的操作状态
      }
    } catch (error) {
      console.error('检查后端状态失败:', error)
      // 出错时保守处理，保持生成状态
    }
  }

  // 组件挂载时的初始化
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // 加载对话列表
        await loadConversations()

        // 自动创建新对话
        await handleNewConversation()
      } catch (error) {
        console.error('初始化应用失败:', error)
      }
    }

    initializeApp()
  }, [])

  // 定期检查用户反馈状态 - 简化逻辑
  useEffect(() => {
    let feedbackCheckInterval: NodeJS.Timeout

    // 只要有活动对话就检查反馈状态
    if (currentConversationId) {
      console.log('🔄 开始定期检查用户反馈状态')

      // 立即检查一次
      checkFeedbackStatus()

      // 每2秒检查一次
      feedbackCheckInterval = setInterval(() => {
        checkFeedbackStatus()
      }, 2000)
    }

    return () => {
      if (feedbackCheckInterval) {
        clearInterval(feedbackCheckInterval)
        console.log('🛑 停止检查用户反馈状态')
      }
    }
  }, [currentConversationId, showFeedbackModal])

  // 创建新智能体消息的公共函数
  const createNewAgentMessage = (prevMessages: ChatMessage[], agentName: string): ChatMessage[] => {
    const newMessage: ChatMessage = {
      id: `ai_${Date.now()}_${agentName}`,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isStreaming: true,
      agentName: agentName
    }

    // 结束之前的流式消息
    return prevMessages.map(msg =>
      msg.isStreaming ? { ...msg, isStreaming: false } : msg
    ).concat(newMessage)
  }

  // 创建欢迎消息的公共函数
  const createWelcomeMessage = (): ChatMessage => ({
    id: 'welcome',
    content: `🚀 **欢迎使用 星河智能！**

我是您的智能AI助手，我可以帮助您：

✨ **回答各种问题** - 从技术问题到日常咨询
🎨 **创意写作** - 诗歌、故事、文案创作
💡 **学习辅导** - 知识解答、概念解释
🔧 **编程帮助** - 代码编写、调试、优化
📊 **数据分析** - 信息整理、趋势分析

请随时向我提问，让我们开始一段精彩的对话吧！`,
    role: 'assistant',
    timestamp: new Date(),
  })

  // 显示欢迎消息
  useEffect(() => {
    if (currentConversationId && messages.length === 0) {
      setMessages([createWelcomeMessage()])
    }
  }, [currentConversationId, messages.length])

  /**
   * 提取附件内容
   */
  const extractAttachmentContent = async (attachments: any[]): Promise<string> => {
    if (!attachments || attachments.length === 0) {
      return ''
    }

    const attachmentContents: string[] = []

    for (const attachment of attachments) {
      try {
        console.log(`📎 正在提取附件内容: ${attachment.original_name}`)
        const response = await fetch(`http://localhost:8000/files/${attachment.file_id}/content`)

        if (!response.ok) {
          console.warn(`⚠️ 无法获取附件 ${attachment.original_name} 的内容`)
          attachmentContents.push(`[附件: ${attachment.original_name} - 无法读取内容]`)
          continue
        }

        const contentData = await response.json()

        if (contentData.type === 'error' || contentData.type === 'unsupported') {
          attachmentContents.push(`[附件: ${attachment.original_name} - ${contentData.content}]`)
        } else {
          // 根据文件类型处理内容
          let fileContent = ''
          switch (contentData.type) {
            case 'text':
            case 'markdown':
              fileContent = typeof contentData.content === 'string'
                ? contentData.content
                : contentData.content.markdown || contentData.content.text || ''
              break
            case 'pdf':
            case 'docx':
              fileContent = contentData.content || ''
              break
            case 'csv':
              // CSV文件，提取文本格式
              if (contentData.content && typeof contentData.content === 'object') {
                fileContent = contentData.content.text ||
                             `CSV数据 (${contentData.content.rows}行 × ${contentData.content.columns}列)`
              } else {
                fileContent = contentData.content || ''
              }
              break
            case 'xlsx':
              // Excel文件，提取所有工作表的文本内容
              if (contentData.content && typeof contentData.content === 'object') {
                const sheets = Object.keys(contentData.content)
                const sheetContents = sheets.map(sheetName => {
                  const sheet = contentData.content[sheetName]
                  return `工作表: ${sheetName} (${sheet.rows}行 × ${sheet.columns}列)\n列名: ${sheet.column_names?.join(', ') || '未知'}`
                })
                fileContent = sheetContents.join('\n\n')
              } else {
                fileContent = contentData.content || ''
              }
              break
            case 'image':
              fileContent = `[图片文件: ${attachment.original_name}]`
              break
            default:
              fileContent = `[${contentData.type}文件: ${attachment.original_name}]`
          }

          attachmentContents.push(`--- 附件: ${attachment.original_name} ---\n${fileContent}`)
        }
      } catch (error) {
        console.error(`❌ 提取附件 ${attachment.original_name} 内容失败:`, error)
        attachmentContents.push(`[附件: ${attachment.original_name} - 提取失败]`)
      }
    }

    return attachmentContents.join('\n\n')
  }

  /**
   * 发送消息处理函数
   */
  const handleSendMessage = async (content: string, attachments?: any[]) => {
    if (!content.trim() && (!attachments || attachments.length === 0)) {
      message.warning('请输入消息内容或添加附件')
      return
    }

    // 确保有conversation_id
    let conversationId = currentConversationId
    if (!conversationId) {
      try {
        conversationId = await chatService.createConversation()
        setCurrentConversationId(conversationId)
        console.log(`📝 创建新对话: ${conversationId}`)
      } catch (error) {
        message.error('创建对话失败')
        return
      }
    }

    // 提取附件内容
    let attachmentContent = ''
    if (attachments && attachments.length > 0) {
      try {
        setIsLoading(true) // 显示加载状态
        message.loading('正在处理附件...', 0)
        attachmentContent = await extractAttachmentContent(attachments)
        message.destroy() // 清除加载消息
        console.log(`📎 附件内容提取完成，长度: ${attachmentContent.length} 字符`)
      } catch (error) {
        message.destroy()
        message.error('处理附件失败')
        setIsLoading(false)
        return
      }
    }

    // 合并用户输入和附件内容
    const combinedContent = attachmentContent
      ? `${content.trim()}\n\n${attachmentContent}`
      : content.trim()

    console.log(`📤 发送内容长度: 用户输入 ${content.trim().length} + 附件内容 ${attachmentContent.length} = 总计 ${combinedContent.length} 字符`)

    // 创建用户消息（显示原始用户输入，不包含附件内容）
    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      content: content.trim(),
      role: 'user',
      timestamp: new Date(),
      attachments: attachments || []
    }

    // 添加用户消息到列表
    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)
    setIsGenerating(true)
    setBackendGenerating(true) // 后端开始生成
    setIsStopped(false) // 重置停止标志
    console.log('📤 发送新消息，重置 canContinue = false')
    setCanContinue(false) // 重置继续状态

    try {
      // 发送合并后的内容（用户输入 + 附件内容）
      await chatService.sendStreamMessage(
        combinedContent,
        // 流式数据回调
        (chunk: string, agentName?: string) => {
          setMessages(prev => {
            // 智能体切换信号 - 创建新消息气泡
            if (chunk === '__NEW_MESSAGE__' && agentName && isValidAgent(agentName)) {
              return createNewAgentMessage(prev, agentName)
            }

            // 添加内容到对应智能体
            if (chunk && chunk !== '__NEW_MESSAGE__') {
              // 查找当前智能体的流式消息
              let targetMessage = prev.find(msg =>
                msg.isStreaming && msg.agentName === agentName
              )

              // 如果没有找到对应智能体的消息，创建一个新的
              if (!targetMessage) {
                const newMessage: ChatMessage = {
                  id: `ai_${Date.now()}_${agentName || 'primary'}`,
                  content: chunk,
                  role: 'assistant',
                  timestamp: new Date(),
                  isStreaming: true,
                  agentName: agentName || 'primary'
                }

                return prev.map(msg =>
                  msg.isStreaming ? { ...msg, isStreaming: false } : msg
                ).concat(newMessage)
              }

              // 添加内容到现有消息
              return prev.map(msg =>
                msg.id === targetMessage.id
                  ? { ...msg, content: msg.content + chunk }
                  : msg
              )
            }

            return prev
          })
        },
        // 完成回调
        () => {
          setMessages(prev => prev.map(msg =>
            msg.isStreaming ? { ...msg, isStreaming: false } : msg
          ))
          setIsLoading(false)
          setIsGenerating(false)
          setBackendGenerating(false) // 后端完成生成
          setCanContinue(false) // 完成时隐藏继续按钮
          console.log('✅ 生成完全完成')
        },
        // 错误回调
        (error: string) => {
          setMessages(prev => {
            // 找到最后一个流式消息并添加错误信息
            const lastStreamingMsg = prev.find(msg => msg.isStreaming)
            if (lastStreamingMsg) {
              return prev.map(msg =>
                msg.id === lastStreamingMsg.id
                  ? { ...msg, content: `❌ 发生错误：${error}`, isStreaming: false }
                  : msg
              )
            } else {
              // 如果没有流式消息，创建一个错误消息
              const errorMessage: ChatMessage = {
                id: `error_${Date.now()}`,
                content: `❌ 发生错误：${error}`,
                role: 'assistant',
                timestamp: new Date(),
                isStreaming: false,
                agentName: 'primary'
              }
              return [...prev, errorMessage]
            }
          })
          setIsLoading(false)
          setIsGenerating(false)
          setBackendGenerating(false) // 错误时后端也停止
          setCanContinue(false) // 错误时不显示继续按钮
          message.error('发送消息失败')
        },
        // 智能体变化回调
        undefined,
        // 用户反馈请求回调
        (prompt: string, agentName?: string) => {
          console.log('🔔 收到用户反馈请求:', prompt, 'from', agentName)
          setFeedbackPrompt(prompt)
          setShowFeedbackModal(true)
          setIsWaitingFeedback(true)
        },
        conversationId
      )
    } catch (error) {
      console.error('发送消息错误:', error)
      setIsLoading(false)
      setIsGenerating(false)
      setBackendGenerating(false) // 错误时后端也停止
      setCanContinue(false) // 错误时不显示继续按钮
      message.error('发送消息失败，请检查网络连接')
    }
  }

  /**
   * 清空所有对话记录
   */
  const handleClearMessages = async () => {
    try {
      // 停止当前的生成任务
      await stopCurrentGeneration()

      // 删除所有对话记录
      const result = await chatService.deleteAllConversations()

      if (result.success) {
        // 清空前端状态
        setMessages([])
        setConversations([])
        setCurrentConversationId('')

        // 重新加载对话列表
        await loadConversations()

        message.success('所有对话记录已清空')
      } else {
        message.error('清空对话记录失败')
      }
    } catch (error) {
      console.error('清空对话记录失败:', error)
      message.error('清空对话记录失败')
    }
  }

  /**
   * 切换侧边栏状态
   */
  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div className="app-container">
      {/* 背景特效 */}
      <BackgroundEffects />
      
      {/* 侧边栏 */}
      <AnimatePresence>
        {!sidebarCollapsed && (
          <motion.div
            initial={{ x: -300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="fixed left-0 sidebar-container"
          >
            <Sidebar
              onClearMessages={handleClearMessages}
              messageCount={messages.length}
              conversationId={currentConversationId}
              conversations={conversations}
              onNewConversation={handleNewConversation}
              onSwitchConversation={handleSwitchConversation}
              onDeleteConversation={handleDeleteConversation}
              isInNewConversation={isInNewConversation()}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* 顶部导航 */}
      <Header
        onToggleSidebar={handleToggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
        messageCount={messages.length}
      />

      {/* 主内容区域 */}
      <div
        className={`main-content ${
          sidebarCollapsed ? 'sidebar-closed' : 'sidebar-open'
        }`}
      >
        <ChatContainer
          messages={messages}
          onSendMessage={handleSendMessage}
          isLoading={backendGenerating} // 只要后端在生成就禁用输入
          isGenerating={isGenerating}
          canContinue={canContinue}
          isThinking={backendGenerating && isGenerating} // 只有在后端生成且前端未暂停时才显示思考状态
          onStopGeneration={handleStopGeneration}
          onContinueGeneration={handleContinueGeneration}
        />
      </div>

      {/* 用户反馈模态框 */}
      <UserFeedbackModal
        visible={showFeedbackModal}
        onClose={() => {
          setShowFeedbackModal(false)
          setIsWaitingFeedback(false)
        }}
        onSubmit={handleUserFeedback}
        conversationId={currentConversationId}
        prompt={feedbackPrompt}
      />
    </div>
  )
}

export default App
