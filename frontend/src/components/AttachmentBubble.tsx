/**
 * 附件气泡组件
 * 显示用户发送的附件，支持点击预览
 */

import React, { useState, memo } from 'react'
import { motion } from 'framer-motion'
import { But<PERSON>, Tooltip } from 'antd'
import { 
  EyeOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  FileMarkdownOutlined,
  FileOutlined
} from '@ant-design/icons'
import FilePreview from './FilePreview'

interface AttachmentBubbleProps {
  attachment: any
  isConsecutive?: boolean
}

/**
 * 获取文件图标
 */
const getFileIcon = (contentType: string, fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  
  if (contentType?.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(ext || '')) {
    return <FileImageOutlined className="text-blue-400" />
  }
  
  if (contentType === 'application/pdf' || ext === 'pdf') {
    return <FilePdfOutlined className="text-red-400" />
  }
  
  if (contentType?.includes('word') || ['doc', 'docx'].includes(ext || '')) {
    return <FileWordOutlined className="text-blue-600" />
  }
  
  if (contentType?.includes('sheet') || ['xls', 'xlsx'].includes(ext || '')) {
    return <FileExcelOutlined className="text-green-600" />
  }
  
  if (contentType?.includes('text') || ['txt'].includes(ext || '')) {
    return <FileTextOutlined className="text-gray-400" />
  }
  
  if (ext === 'md') {
    return <FileMarkdownOutlined className="text-purple-400" />
  }
  
  return <FileOutlined className="text-gray-400" />
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

/**
 * 附件气泡组件
 */
const AttachmentBubble: React.FC<AttachmentBubbleProps> = memo(({
  attachment,
  isConsecutive = false
}) => {
  const [showPreview, setShowPreview] = useState(false)

  // 处理预览点击
  const handlePreview = () => {
    setShowPreview(true)
  }

  return (
    <>
      <motion.div
        className="relative group ml-auto"
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        {/* 附件气泡 */}
        <motion.div
          className={`relative rounded-2xl shadow-lg bg-gradient-to-r from-slate-600 to-slate-500 text-white rounded-br-md cursor-pointer ${
            isConsecutive ? 'rounded-tr-md' : ''
          }`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          onClick={handlePreview}
        >
          {/* 附件内容 */}
          <div className="px-4 py-3">
            <div className="flex items-center space-x-3">
              {/* 文件图标 */}
              <div className="flex-shrink-0 text-2xl">
                {getFileIcon(attachment.content_type, attachment.original_name)}
              </div>
              
              {/* 文件信息 */}
              <div className="flex-1 min-w-0">
                <div className="font-medium text-white truncate">
                  {attachment.original_name}
                </div>
                <div className="text-sm text-slate-200 opacity-80">
                  {formatFileSize(attachment.file_size)}
                </div>
              </div>
              
              {/* 预览图标 */}
              <div className="flex-shrink-0">
                <EyeOutlined className="text-white text-lg opacity-80" />
              </div>
            </div>
          </div>


        </motion.div>

        {/* 消息尾巴（箭头） */}
        <div className="absolute top-4 right-0 translate-x-1 w-0 h-0 border-l-8 border-l-slate-500 border-t-4 border-t-transparent border-b-4 border-b-transparent" />
      </motion.div>

      {/* 文件预览 */}
      <FilePreview
        visible={showPreview}
        onClose={() => setShowPreview(false)}
        fileInfo={attachment}
      />
    </>
  )
})

// 设置displayName用于调试
AttachmentBubble.displayName = 'AttachmentBubble'

export default AttachmentBubble
