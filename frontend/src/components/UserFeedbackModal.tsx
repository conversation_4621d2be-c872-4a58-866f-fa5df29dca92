/**
 * 用户反馈模态框组件
 * 用于收集用户对AI生成内容的反馈
 */

import React, { useState, useEffect } from 'react'
import { Modal, Input, Button, Radio, Space, message, Divider } from 'antd'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CheckCircleOutlined, 
  EditOutlined, 
  CloseCircleOutlined,
  MessageOutlined,
  SendOutlined
} from '@ant-design/icons'

const { TextArea } = Input

interface UserFeedbackModalProps {
  visible: boolean
  onClose: () => void
  onSubmit: (feedback: UserFeedback) => void
  conversationId?: string
  prompt?: string  // UserProxyAgent的提示信息
}

interface UserFeedback {
  content: string
  feedback_type: 'approve' | 'modify' | 'reject' | 'general'
  conversation_id?: string
}

/**
 * 用户反馈模态框组件
 */
const UserFeedbackModal: React.FC<UserFeedbackModalProps> = ({
  visible,
  onClose,
  onSubmit,
  conversationId,
  prompt = "请提供您的反馈意见"
}) => {
  const [feedbackType, setFeedbackType] = useState<'approve' | 'modify' | 'reject' | 'general'>('approve')
  const [content, setContent] = useState('')
  const [loading, setLoading] = useState(false)

  // 重置表单
  useEffect(() => {
    if (visible) {
      setFeedbackType('approve')
      setContent('')
      setLoading(false)
    }
  }, [visible])

  // 处理提交
  const handleSubmit = async () => {
    if (!content.trim() && feedbackType !== 'approve') {
      message.warning('请输入反馈内容')
      return
    }

    setLoading(true)
    try {
      const feedback: UserFeedback = {
        content: content.trim() || (feedbackType === 'approve' ? 'APPROVE' : ''),
        feedback_type: feedbackType,
        conversation_id: conversationId
      }

      await onSubmit(feedback)
      message.success('反馈已提交')
      onClose()
    } catch (error) {
      message.error('提交反馈失败')
      console.error('提交反馈失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 快速批准
  const handleQuickApprove = async () => {
    setLoading(true)
    try {
      const feedback: UserFeedback = {
        content: 'APPROVE',
        feedback_type: 'approve',
        conversation_id: conversationId
      }

      await onSubmit(feedback)
      message.success('已批准')
      onClose()
    } catch (error) {
      message.error('提交失败')
      console.error('快速批准失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AnimatePresence>
      {visible && (
        <Modal
          title={
            <div className="flex items-center space-x-2">
              <MessageOutlined className="text-neon-blue" />
              <span>用户反馈</span>
            </div>
          }
          open={visible}
          footer={null}
          width={600}
          className="user-feedback-modal"
          maskClosable={false}
          closable={false}
          keyboard={false}
          destroyOnClose
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* 提示信息 */}
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <div className="text-sm text-gray-600 mb-2">AI智能体请求:</div>
              <div className="text-gray-800 font-medium">{prompt}</div>
            </div>

            {/* 快速操作按钮 */}
            <div className="mb-6">
              <div className="text-sm text-gray-600 mb-3">快速操作:</div>
              <Space size="middle">
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={handleQuickApprove}
                  loading={loading}
                  className="bg-green-500 hover:bg-green-600 border-green-500"
                >
                  批准继续
                </Button>
                <Button
                  icon={<EditOutlined />}
                  onClick={() => setFeedbackType('modify')}
                  className="border-orange-400 text-orange-600 hover:border-orange-500 hover:text-orange-700"
                >
                  需要修改
                </Button>
                <Button
                  icon={<CloseCircleOutlined />}
                  onClick={() => setFeedbackType('reject')}
                  className="border-red-400 text-red-600 hover:border-red-500 hover:text-red-700"
                >
                  拒绝方案
                </Button>
              </Space>
            </div>

            <Divider />

            {/* 详细反馈 */}
            <div className="space-y-4">
              <div>
                <div className="text-sm text-gray-600 mb-2">反馈类型:</div>
                <Radio.Group
                  value={feedbackType}
                  onChange={(e) => setFeedbackType(e.target.value)}
                  className="w-full"
                >
                  <Space direction="vertical" className="w-full">
                    <Radio value="approve" className="text-green-600">
                      <span className="ml-2">批准 - 同意当前方案</span>
                    </Radio>
                    <Radio value="modify" className="text-orange-600">
                      <span className="ml-2">修改 - 需要调整和改进</span>
                    </Radio>
                    <Radio value="reject" className="text-red-600">
                      <span className="ml-2">拒绝 - 不同意当前方案</span>
                    </Radio>
                    <Radio value="general" className="text-blue-600">
                      <span className="ml-2">一般反馈 - 其他意见</span>
                    </Radio>
                  </Space>
                </Radio.Group>
              </div>

              <div>
                <div className="text-sm text-gray-600 mb-2">
                  详细说明 {feedbackType === 'approve' ? '(可选)' : '(必填)'}:
                </div>
                <TextArea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder={
                    feedbackType === 'approve' 
                      ? '可以添加额外的确认信息...'
                      : feedbackType === 'modify'
                      ? '请说明需要如何修改...'
                      : feedbackType === 'reject'
                      ? '请说明拒绝的原因...'
                      : '请输入您的反馈意见...'
                  }
                  rows={4}
                  className="resize-none"
                />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end mt-6 pt-4 border-t border-gray-200">
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSubmit}
                loading={loading}
                className="bg-gradient-to-r from-neon-blue to-neon-purple border-none"
                size="large"
              >
                提交反馈
              </Button>
            </div>
          </motion.div>
        </Modal>
      )}
    </AnimatePresence>
  )
}

export default UserFeedbackModal
