/**
 * 用户反馈模态框组件
 * 用于收集用户对AI生成内容的反馈
 */

import React, { useState, useEffect } from 'react'
import { Card, Input, Button, Space, message } from 'antd'
import { motion, AnimatePresence } from 'framer-motion'
import {
  CheckCircleOutlined,
  EditOutlined,
  CloseCircleOutlined,
  MessageOutlined,
  SendOutlined,
  CloseOutlined
} from '@ant-design/icons'

const { TextArea } = Input

interface UserFeedbackModalProps {
  visible: boolean
  onClose: () => void
  onSubmit: (feedback: UserFeedback) => void
  conversationId?: string
  prompt?: string  // UserProxyAgent的提示信息
}

interface UserFeedback {
  content: string
  feedback_type: 'approve' | 'modify' | 'reject' | 'general'
  conversation_id?: string
}

/**
 * 用户反馈模态框组件
 */
const UserFeedbackModal: React.FC<UserFeedbackModalProps> = ({
  visible,
  onClose,
  onSubmit,
  conversationId,
  prompt = "请提供您的反馈意见"
}) => {
  const [feedbackType, setFeedbackType] = useState<'approve' | 'modify' | 'reject' | 'general'>('approve')
  const [content, setContent] = useState('')
  const [loading, setLoading] = useState(false)

  // 重置表单
  useEffect(() => {
    if (visible) {
      setFeedbackType('approve')
      setContent('')
      setLoading(false)
    }
  }, [visible])

  // 处理提交
  const handleSubmit = async () => {
    if (!content.trim() && feedbackType !== 'approve') {
      message.warning('请输入反馈内容')
      return
    }

    setLoading(true)
    try {
      const feedback: UserFeedback = {
        content: content.trim() || (feedbackType === 'approve' ? 'APPROVE' : ''),
        feedback_type: feedbackType,
        conversation_id: conversationId
      }

      await onSubmit(feedback)
      // 移除这里的message.success，由父组件处理
      onClose()
    } catch (error) {
      message.error('提交反馈失败')
      console.error('提交反馈失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 快速批准
  const handleQuickApprove = async () => {
    setLoading(true)
    try {
      const feedback: UserFeedback = {
        content: 'APPROVE',
        feedback_type: 'approve',
        conversation_id: conversationId
      }

      await onSubmit(feedback)
      // 移除这里的message.success，由父组件处理
      onClose()
    } catch (error) {
      message.error('提交失败')
      console.error('快速批准失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 终止对话
  const handleTerminate = async () => {
    setLoading(true)
    try {
      const feedback: UserFeedback = {
        content: 'TERMINATE',
        feedback_type: 'approve',
        conversation_id: conversationId
      }

      await onSubmit(feedback)
      // 移除这里的message.success，由父组件处理
      onClose()
    } catch (error) {
      message.error('提交失败')
      console.error('终止对话失败:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
          style={{
            position: 'fixed',
            bottom: 20,
            right: 20,
            width: 320,
            zIndex: 1000,
          }}
        >
          <Card
            className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm"
            style={{
              borderRadius: '16px',
              boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)',
            }}
            title={
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MessageOutlined className="text-blue-500" />
                  <span className="text-sm font-medium">用户反馈</span>
                </div>
                <Button
                  type="text"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                />
              </div>
            }
            bodyStyle={{ padding: '16px' }}
          >
            <div className="space-y-3">
              {/* 提示信息 */}
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-xs text-gray-500 mb-1">AI智能体请求:</div>
                <div className="text-sm text-gray-700 font-medium">{prompt}</div>
              </div>

              {/* 快速操作按钮 */}
              <div className="space-y-2">
                <div className="text-xs text-gray-500">快速操作:</div>
                <Space direction="vertical" size="small" className="w-full">
                  <Button
                    type="primary"
                    icon={<CheckCircleOutlined />}
                    onClick={handleQuickApprove}
                    loading={loading}
                    className="w-full bg-green-500 hover:bg-green-600 border-green-500"
                    size="small"
                  >
                    批准继续
                  </Button>
                  <div className="flex space-x-2">
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => setFeedbackType('modify')}
                      className="flex-1 border-orange-400 text-orange-600 hover:border-orange-500 hover:text-orange-700"
                      size="small"
                    >
                      修改
                    </Button>
                    <Button
                      icon={<CloseCircleOutlined />}
                      onClick={() => setFeedbackType('reject')}
                      className="flex-1 border-red-400 text-red-600 hover:border-red-500 hover:text-red-700"
                      size="small"
                    >
                      拒绝
                    </Button>
                  </div>
                  <Button
                    type="default"
                    onClick={handleTerminate}
                    loading={loading}
                    className="w-full border-gray-400 text-gray-600 hover:border-gray-500 hover:text-gray-700"
                    size="small"
                  >
                    结束对话
                  </Button>
                </Space>
              </div>

              {/* 自定义反馈输入 */}
              {feedbackType !== 'approve' && (
                <div className="space-y-2">
                  <div className="text-xs text-gray-500">详细说明:</div>
                  <TextArea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder={
                      feedbackType === 'modify'
                        ? '请说明需要如何修改...'
                        : feedbackType === 'reject'
                        ? '请说明拒绝的原因...'
                        : '请输入您的反馈意见...'
                    }
                    rows={3}
                    className="resize-none text-sm"
                  />
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSubmit}
                    loading={loading}
                    className="w-full bg-blue-500 hover:bg-blue-600 border-blue-500"
                    size="small"
                  >
                    提交反馈
                  </Button>
                </div>
              )}
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default UserFeedbackModal
