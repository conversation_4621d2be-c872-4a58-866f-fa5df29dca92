/**
 * 打字机效果测试组件
 */

import React, { useState } from 'react'
import { Button, Space } from 'antd'
import TypewriterText from './TypewriterText'

const TypewriterTest: React.FC = () => {
  const [content, setContent] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)

  const testTexts = [
    '你好！我是AI助手，很高兴为您服务。',
    '这是一个打字机效果的测试。Hello, this is a typewriter effect test.',
    '支持中文、English、数字123和标点符号！？。',
    '```javascript\nconst hello = "world";\nconsole.log(hello);\n```',
    '# 标题\n\n这是一个**粗体**文本和*斜体*文本的示例。\n\n- 列表项1\n- 列表项2\n- 列表项3'
  ]

  const startTest = (index: number) => {
    setContent('')
    setIsStreaming(true)
    
    const text = testTexts[index]
    let currentIndex = 0
    
    const addChar = () => {
      if (currentIndex < text.length) {
        setContent(text.slice(0, currentIndex + 1))
        currentIndex++
        setTimeout(addChar, 50) // 模拟流式输入
      } else {
        setIsStreaming(false)
      }
    }
    
    setTimeout(addChar, 100)
  }

  const stopTest = () => {
    setIsStreaming(false)
  }

  const clearTest = () => {
    setContent('')
    setIsStreaming(false)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-white">打字机效果测试</h2>
      
      <Space wrap className="mb-4">
        {testTexts.map((_, index) => (
          <Button 
            key={index}
            onClick={() => startTest(index)}
            disabled={isStreaming}
          >
            测试 {index + 1}
          </Button>
        ))}
        <Button onClick={stopTest} disabled={!isStreaming}>
          停止
        </Button>
        <Button onClick={clearTest}>
          清空
        </Button>
      </Space>

      <div className="glass rounded-lg p-4 min-h-[200px]">
        <TypewriterText
          content={content}
          isStreaming={isStreaming}
          speed={30}
        />
      </div>

      <div className="mt-4 text-sm text-slate-400">
        <p>状态: {isStreaming ? '正在输入...' : '已完成'}</p>
        <p>内容长度: {content.length} 字符</p>
      </div>
    </div>
  )
}

export default TypewriterTest
