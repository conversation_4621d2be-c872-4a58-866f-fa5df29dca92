/**
 * 文件预览组件
 * 支持多种文件格式的预览显示
 */

import React, { useState, useEffect } from 'react'
import { Drawer, Spin, Alert, Tabs, Table, Image, Button, Typography } from 'antd'
import {
  CloseOutlined,
  DownloadOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  FileMarkdownOutlined
} from '@ant-design/icons'
import FileToMarkdownConverter from './FileToMarkdownConverter'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

interface FilePreviewProps {
  visible: boolean
  onClose: () => void
  fileInfo: any
}

interface FileContent {
  type: string
  content: any
  metadata?: any
  file_info: any
}

const FilePreview: React.FC<FilePreviewProps> = ({
  visible,
  onClose,
  fileInfo
}) => {
  const [loading, setLoading] = useState(false)
  const [content, setContent] = useState<FileContent | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 获取文件内容
  const fetchFileContent = async () => {
    if (!fileInfo?.file_id) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`http://localhost:8000/files/${fileInfo.file_id}/content`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || '获取文件内容失败')
      }

      const contentData: FileContent = await response.json()
      setContent(contentData)
      
    } catch (err) {
      console.error('获取文件内容失败:', err)
      setError(err instanceof Error ? err.message : '获取文件内容失败')
    } finally {
      setLoading(false)
    }
  }

  // 当文件信息变化时重新获取内容
  useEffect(() => {
    if (visible && fileInfo) {
      fetchFileContent()
    }
  }, [visible, fileInfo])

  // 获取文件图标
  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) {
      return <FileImageOutlined style={{ color: '#52c41a', fontSize: '24px' }} />
    } else if (contentType === 'application/pdf') {
      return <FilePdfOutlined style={{ color: '#ff4d4f', fontSize: '24px' }} />
    } else if (contentType.includes('word')) {
      return <FileWordOutlined style={{ color: '#1890ff', fontSize: '24px' }} />
    } else if (contentType.includes('sheet')) {
      return <FileExcelOutlined style={{ color: '#52c41a', fontSize: '24px' }} />
    } else if (content?.type === 'markdown') {
      return <FileMarkdownOutlined style={{ color: '#722ed1', fontSize: '24px' }} />
    } else {
      return <FileTextOutlined style={{ color: '#8c8c8c', fontSize: '24px' }} />
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 渲染图片内容
  const renderImageContent = () => {
    if (content?.type !== 'image') return null

    return (
      <div className="image-preview">
        <Image
          src={content.content}
          alt={fileInfo.original_name}
          style={{ maxWidth: '100%', maxHeight: '600px' }}
          preview={{
            mask: '点击预览'
          }}
        />
        {content.metadata && (
          <div className="image-metadata mt-4">
            <Text type="secondary">
              尺寸: {content.metadata.width} × {content.metadata.height} | 
              格式: {content.metadata.format} | 
              模式: {content.metadata.mode}
            </Text>
          </div>
        )}
      </div>
    )
  }

  // 渲染PDF内容
  const renderPdfContent = () => {
    if (content?.type !== 'pdf') return null

    return (
      <div className="pdf-preview">
        <div className="pdf-metadata mb-4">
          {content.metadata && (
            <div>
              <Text strong>页数: </Text>
              <Text>{content.metadata.pages}</Text>
              {content.metadata.title && (
                <>
                  <br />
                  <Text strong>标题: </Text>
                  <Text>{content.metadata.title}</Text>
                </>
              )}
              {content.metadata.author && (
                <>
                  <br />
                  <Text strong>作者: </Text>
                  <Text>{content.metadata.author}</Text>
                </>
              )}
            </div>
          )}
        </div>
        <div className="pdf-text-content">
          <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
            {content.content}
          </pre>
        </div>
      </div>
    )
  }

  // 渲染Excel内容
  const renderExcelContent = () => {
    if (content?.type !== 'xlsx') return null

    const sheets = Object.keys(content.content)

    return (
      <div className="excel-preview">
        <Tabs defaultActiveKey={sheets[0]} type="card">
          {sheets.map(sheetName => {
            const sheetData = content.content[sheetName]
            return (
              <TabPane tab={sheetName} key={sheetName}>
                <div className="sheet-info mb-3">
                  <Text type="secondary">
                    {sheetData.rows} 行 × {sheetData.columns} 列
                  </Text>
                </div>
                <div 
                  className="excel-table-container"
                  dangerouslySetInnerHTML={{ __html: sheetData.html }}
                />
              </TabPane>
            )
          })}
        </Tabs>
      </div>
    )
  }

  // 渲染CSV内容
  const renderCsvContent = () => {
    if (content?.type !== 'csv') return null

    return (
      <div className="csv-preview">
        <div className="csv-info mb-3">
          <Text type="secondary">
            {content.content.rows} 行 × {content.content.columns} 列
          </Text>
        </div>
        <div 
          className="csv-table-container"
          dangerouslySetInnerHTML={{ __html: content.content.html }}
        />
      </div>
    )
  }

  // 渲染Markdown内容
  const renderMarkdownContent = () => {
    if (content?.type !== 'markdown') return null

    return (
      <div className="markdown-preview">
        <Tabs defaultActiveKey="preview" type="card">
          <TabPane tab="预览" key="preview">
            <div 
              className="markdown-html"
              dangerouslySetInnerHTML={{ __html: content.content.html }}
            />
          </TabPane>
          <TabPane tab="源码" key="source">
            <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
              {content.content.markdown}
            </pre>
          </TabPane>
        </Tabs>
      </div>
    )
  }

  // 渲染文本内容
  const renderTextContent = () => {
    if (!['text', 'docx'].includes(content?.type || '')) return null

    return (
      <div className="text-preview">
        {content.metadata && (
          <div className="text-metadata mb-3">
            <Text type="secondary">
              {content.metadata.lines && `${content.metadata.lines} 行`}
              {content.metadata.characters && ` | ${content.metadata.characters} 字符`}
              {content.metadata.words && ` | ${content.metadata.words} 单词`}
              {content.metadata.encoding && ` | 编码: ${content.metadata.encoding}`}
            </Text>
          </div>
        )}
        <div className="text-content">
          <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
            {content.content}
          </pre>
        </div>
      </div>
    )
  }

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" tip="正在加载文件内容..." />
        </div>
      )
    }

    if (error) {
      return (
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchFileContent}>
              重试
            </Button>
          }
        />
      )
    }

    if (!content) {
      return (
        <Alert
          message="暂无内容"
          description="无法获取文件内容"
          type="info"
          showIcon
        />
      )
    }

    // 根据文件类型渲染不同的内容
    switch (content.type) {
      case 'image':
        return renderImageContent()
      case 'pdf':
        return renderPdfContent()
      case 'xlsx':
        return renderExcelContent()
      case 'csv':
        return renderCsvContent()
      case 'markdown':
        return renderMarkdownContent()
      case 'text':
      case 'docx':
        return renderTextContent()
      case 'unsupported':
        return (
          <Alert
            message="不支持预览"
            description={content.content || `暂不支持预览此类型的文件`}
            type="warning"
            showIcon
          />
        )
      case 'error':
        return (
          <Alert
            message="文件处理错误"
            description={content.content || "处理文件时发生错误"}
            type="error"
            showIcon
            action={
              <Button size="small" onClick={fetchFileContent}>
                重试
              </Button>
            }
          />
        )
      default:
        return (
          <Alert
            message="未知文件类型"
            description={`暂不支持预览 ${content.type} 类型的文件`}
            type="warning"
            showIcon
          />
        )
    }
  }

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {fileInfo && getFileIcon(fileInfo.content_type)}
            <div className="ml-3">
              <div className="font-medium">{fileInfo?.original_name}</div>
              <div className="text-sm text-slate-400">
                {fileInfo && formatFileSize(fileInfo.file_size)}
              </div>
            </div>
          </div>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
          />
        </div>
      }
      placement="right"
      width="60%"
      open={visible}
      onClose={onClose}
      closable={false}
      className="file-preview-drawer"
    >
      <div className="file-preview-content">
        <Tabs defaultActiveKey="preview" type="card">
          <Tabs.TabPane tab="原始预览" key="preview">
            {renderContent()}
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={
              <span>
                <FileMarkdownOutlined />
                Markdown转换
              </span>
            }
            key="markdown"
          >
            <FileToMarkdownConverter
              fileInfo={fileInfo}
              visible={visible}
            />
          </Tabs.TabPane>
        </Tabs>
      </div>
    </Drawer>
  )
}

export default FilePreview
