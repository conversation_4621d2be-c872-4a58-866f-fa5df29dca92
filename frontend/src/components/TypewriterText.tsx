/**
 * 打字机效果组件
 * 实现一个字一个字的稳定输出效果
 */

import React, { useState, useEffect, useRef, memo } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'

interface TypewriterTextProps {
  content: string
  isStreaming: boolean
  speed?: number // 打字速度，毫秒/字符
  markdownComponents?: any // Markdown组件配置
}

// 获取动态打字速度
const getTypingSpeed = (char: string, baseSpeed: number): number => {
  // 中文字符稍慢一些
  if (/[\u4e00-\u9fff]/.test(char)) {
    return baseSpeed + 10
  }
  // 标点符号稍快一些
  if (/[.,!?;:]/.test(char)) {
    return Math.max(baseSpeed - 10, 10)
  }
  // 空格很快
  if (char === ' ') {
    return Math.max(baseSpeed - 15, 5)
  }
  // 换行符稍慢
  if (char === '\n') {
    return baseSpeed + 20
  }
  return baseSpeed
}

/**
 * 打字机文本组件
 */
const TypewriterText: React.FC<TypewriterTextProps> = memo(({
  content,
  isStreaming,
  speed = 30, // 默认30ms一个字符
  markdownComponents
}) => {
  const [displayedContent, setDisplayedContent] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const contentRef = useRef('')

  // 清理定时器
  const clearTimer = () => {
    if (intervalRef.current) {
      clearTimeout(intervalRef.current)
      intervalRef.current = null
    }
  }

  // 重置状态
  const resetState = () => {
    clearTimer()
    setDisplayedContent('')
    setCurrentIndex(0)
  }

  // 当内容变化时处理打字机效果
  useEffect(() => {
    // 如果内容为空，重置状态
    if (!content) {
      resetState()
      contentRef.current = ''
      return
    }

    // 如果内容没有变化，不需要重新开始
    if (content === contentRef.current) {
      return
    }

    // 更新内容引用
    contentRef.current = content

    // 如果不是流式输出，直接显示全部内容
    if (!isStreaming) {
      clearTimer()
      setDisplayedContent(content)
      setCurrentIndex(content.length)
      return
    }

    // 如果新内容比当前显示的内容短，说明是新消息，重新开始
    if (content.length < displayedContent.length) {
      resetState()
    }

    // 如果当前显示的内容已经是最新的，不需要继续
    if (displayedContent === content) {
      clearTimer()
      return
    }

    // 开始打字机效果
    clearTimer()

    // 从当前位置继续打字
    let index = Math.min(currentIndex, content.length)

    const typeNextChar = () => {
      if (index >= content.length) {
        clearTimer()
        return
      }

      // 逐字符添加，支持中文字符
      const currentChar = content[index]
      setDisplayedContent(content.slice(0, index + 1))
      setCurrentIndex(index + 1)
      index++

      // 根据字符类型动态调整下一个字符的打字速度
      if (index < content.length) {
        const nextChar = content[index]
        const nextSpeed = getTypingSpeed(nextChar, speed)
        intervalRef.current = setTimeout(typeNextChar, nextSpeed)
      }
    }

    // 开始第一个字符的打字
    if (index < content.length) {
      const firstChar = content[index]
      const firstSpeed = getTypingSpeed(firstChar, speed)
      intervalRef.current = setTimeout(typeNextChar, firstSpeed)
    }

    // 清理函数
    return () => {
      clearTimer()
    }
  }, [content, isStreaming, speed])

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearTimer()
    }
  }, [])

  // 当流式输出结束时，确保显示完整内容
  useEffect(() => {
    if (!isStreaming && content && displayedContent !== content) {
      clearTimer()
      setDisplayedContent(content)
      setCurrentIndex(content.length)
    }
  }, [isStreaming, content, displayedContent])

  return (
    <div className="typewriter-container">
      {markdownComponents ? (
        // 使用Markdown渲染
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={markdownComponents}
          skipHtml={false}
        >
          {displayedContent}
        </ReactMarkdown>
      ) : (
        // 纯文本渲染
        <span className="whitespace-pre-wrap break-words">
          {displayedContent}
        </span>
      )}

      {/* 打字机光标 */}
      {isStreaming && (
        <span className="typewriter-cursor animate-pulse ml-1 text-neon-blue">
          |
        </span>
      )}
    </div>
  )
})

// 设置displayName用于调试
TypewriterText.displayName = 'TypewriterText'

export default TypewriterText
