/**
 * 文件到Markdown转换器组件
 * 支持多种文件格式转换为Markdown格式
 */

import React, { useState, useEffect } from 'react'
import { Card, Button, Spin, Alert, Tabs, Typography, message, Space, Tooltip } from 'antd'
import { 
  FileMarkdownOutlined,
  DownloadOutlined,
  CopyOutlined,
  EyeOutlined,
  ReloadOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import { motion } from 'framer-motion'

const { Title, Paragraph, Text } = Typography
const { TabPane } = Tabs

interface FileToMarkdownConverterProps {
  fileInfo: any
  visible?: boolean
  onClose?: () => void
}

interface MarkdownContent {
  type: string
  content: {
    markdown: string
    html: string
  }
  metadata?: {
    lines: number
    characters: number
    source_type: string
    conversion_method: string
  }
  file_info: any
}

const FileToMarkdownConverter: React.FC<FileToMarkdownConverterProps> = ({
  fileInfo,
  visible = true,
  onClose
}) => {
  const [loading, setLoading] = useState(false)
  const [markdownContent, setMarkdownContent] = useState<MarkdownContent | null>(null)
  const [error, setError] = useState<string | null>(null)

  // 获取文件的Markdown内容
  const fetchMarkdownContent = async () => {
    if (!fileInfo?.file_id) return

    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`http://localhost:8000/files/${fileInfo.file_id}/markdown`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || '获取Markdown内容失败')
      }

      const contentData: MarkdownContent = await response.json()
      setMarkdownContent(contentData)
      
    } catch (err) {
      console.error('获取Markdown内容失败:', err)
      setError(err instanceof Error ? err.message : '获取Markdown内容失败')
    } finally {
      setLoading(false)
    }
  }

  // 当文件信息变化时重新获取内容
  useEffect(() => {
    if (visible && fileInfo) {
      fetchMarkdownContent()
    }
  }, [visible, fileInfo])

  // 复制Markdown内容到剪贴板
  const copyToClipboard = async () => {
    if (!markdownContent?.content?.markdown) return

    try {
      await navigator.clipboard.writeText(markdownContent.content.markdown)
      message.success('Markdown内容已复制到剪贴板')
    } catch (err) {
      message.error('复制失败，请手动选择复制')
    }
  }

  // 下载Markdown文件
  const downloadMarkdown = () => {
    if (!markdownContent?.content?.markdown) return

    const blob = new Blob([markdownContent.content.markdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${fileInfo.original_name || 'converted'}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    message.success('Markdown文件下载成功')
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" tip="正在转换为Markdown格式..." />
        </div>
      )
    }

    if (error) {
      return (
        <Alert
          message="转换失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchMarkdownContent}>
              重试
            </Button>
          }
        />
      )
    }

    if (!markdownContent) {
      return (
        <Alert
          message="暂无内容"
          description="无法获取Markdown内容"
          type="info"
          showIcon
        />
      )
    }

    if (markdownContent.type === 'error') {
      return (
        <Alert
          message="转换错误"
          description={markdownContent.content?.markdown || "转换过程中发生错误"}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchMarkdownContent}>
              重试
            </Button>
          }
        />
      )
    }

    return (
      <div className="markdown-converter-content">
        {/* 元数据信息 */}
        {markdownContent.metadata && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <Text type="secondary" className="text-sm">
              <Space split="|">
                <span>行数: {markdownContent.metadata.lines}</span>
                <span>字符: {markdownContent.metadata.characters}</span>
                <span>源格式: {markdownContent.metadata.source_type}</span>
                <span>转换方式: {markdownContent.metadata.conversion_method === 'marker' ? 'Marker (高质量)' : '基础转换'}</span>
              </Space>
            </Text>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="mb-4">
          <Space>
            <Tooltip title="复制Markdown内容">
              <Button 
                icon={<CopyOutlined />} 
                onClick={copyToClipboard}
                type="primary"
              >
                复制
              </Button>
            </Tooltip>
            <Tooltip title="下载Markdown文件">
              <Button 
                icon={<DownloadOutlined />} 
                onClick={downloadMarkdown}
              >
                下载
              </Button>
            </Tooltip>
            <Tooltip title="重新转换">
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchMarkdownContent}
              >
                刷新
              </Button>
            </Tooltip>
          </Space>
        </div>

        {/* Markdown内容展示 */}
        <Tabs defaultActiveKey="preview" type="card">
          <TabPane 
            tab={
              <span>
                <EyeOutlined />
                预览
              </span>
            } 
            key="preview"
          >
            <div 
              className="markdown-preview prose max-w-none"
              dangerouslySetInnerHTML={{ __html: markdownContent.content.html }}
              style={{
                maxHeight: '600px',
                overflowY: 'auto',
                padding: '16px',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                backgroundColor: '#fafafa'
              }}
            />
          </TabPane>
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                源码
              </span>
            } 
            key="source"
          >
            <pre 
              style={{ 
                whiteSpace: 'pre-wrap', 
                fontFamily: 'monospace',
                maxHeight: '600px',
                overflowY: 'auto',
                padding: '16px',
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                backgroundColor: '#fafafa'
              }}
            >
              {markdownContent.content.markdown}
            </pre>
          </TabPane>
        </Tabs>
      </div>
    )
  }

  if (!visible) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileMarkdownOutlined className="text-purple-500 mr-2" />
              <div>
                <div className="font-medium">Markdown转换器</div>
                <div className="text-sm text-gray-500">
                  {fileInfo?.original_name} ({fileInfo && formatFileSize(fileInfo.file_size)})
                </div>
              </div>
            </div>
            {onClose && (
              <Button type="text" onClick={onClose}>
                关闭
              </Button>
            )}
          </div>
        }
        className="markdown-converter-card"
        bodyStyle={{ padding: '16px' }}
      >
        {renderContent()}
      </Card>
    </motion.div>
  )
}

export default FileToMarkdownConverter
