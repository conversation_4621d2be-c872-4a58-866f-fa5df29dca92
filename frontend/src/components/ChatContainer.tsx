/**
 * 聊天容器组件
 * 包含消息列表和输入框的主要聊天界面
 */

import React, { useRef, useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import MessageList from './MessageList'
import MessageInput from './MessageInput'
import LoadingIndicator from './LoadingIndicator'
import { ChatMessage } from '../services/chatService'

interface ChatContainerProps {
  messages: ChatMessage[]
  onSendMessage: (message: string, attachments?: any[]) => void
  isLoading: boolean
  isGenerating?: boolean
  canContinue?: boolean
  onStopGeneration?: () => void
  onContinueGeneration?: () => void
  isThinking?: boolean // 是否显示AI思考状态
}

/**
 * 聊天容器组件
 */
const ChatContainer: React.FC<ChatContainerProps> = ({
  messages,
  onSendMessage,
  isLoading,
  isGenerating = false,
  canContinue = false,
  onStopGeneration,
  onContinueGeneration,
  isThinking = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastScrollTop = useRef<number>(0)
  const lastMessageCount = useRef<number>(0)

  // 自动滚动到底部 - 优化流式输出滚动
  const scrollToBottom = (immediate = false) => {
    if (!messagesEndRef.current) return

    if (immediate) {
      // 立即滚动，用于流式输出
      messagesEndRef.current.scrollIntoView({
        behavior: 'auto',
        block: 'end'
      })
    } else {
      // 平滑滚动，用于普通消息
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      })
    }
  }

  // 检测是否有流式消息
  const hasStreamingMessage = messages.some(msg => msg.isStreaming)

  // 检测用户是否在滚动查看历史消息
  const handleScroll = () => {
    if (!messagesContainerRef.current) return

    const container = messagesContainerRef.current
    const currentScrollTop = container.scrollTop
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 50

    // 检测用户是否主动向上滚动
    const isScrollingUp = currentScrollTop < lastScrollTop.current
    lastScrollTop.current = currentScrollTop

    if (isScrollingUp && !isAtBottom) {
      // 用户主动向上滚动，禁用自动滚动
      setIsUserScrolling(true)
      setAutoScrollEnabled(false)
      console.log('🔒 用户向上滚动，禁用自动滚动')

      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    } else if (isAtBottom) {
      // 用户滚动到底部，重新启用自动滚动
      setIsUserScrolling(false)
      setAutoScrollEnabled(true)
      console.log('🔓 用户回到底部，启用自动滚动')

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }

  // 当消息更新时自动滚动 - 优化流式输出滚动
  useEffect(() => {
    if (hasStreamingMessage) {
      // 流式输出时强制滚动，忽略其他限制
      scrollToBottom(true)
      console.log('📜 流式输出中，强制滚动到底部')
      return
    }

    // 非流式输出时应用智能滚动控制
    if (!autoScrollEnabled || isUserScrolling) return

    // 普通消息使用延迟滚动
    const lastMessage = messages[messages.length - 1]
    if (lastMessage && !lastMessage.isStreaming) {
      const timer = setTimeout(() => {
        scrollToBottom(false)
        console.log('📜 普通消息滚动到底部')
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [messages, hasStreamingMessage])

  // 流式输出完成后的智能滚动控制
  useEffect(() => {
    if (!hasStreamingMessage && messages.length > 0) {
      const lastMessage = messages[messages.length - 1]
      if (lastMessage && lastMessage.role === 'assistant' && !lastMessage.isStreaming) {
        // AI完成输出后，给用户时间查看，然后禁用自动滚动
        const timer = setTimeout(() => {
          console.log('⏸️ AI输出完成，禁用自动滚动以便用户查看')
          setAutoScrollEnabled(false)
        }, 3000) // 减少到3秒
        return () => clearTimeout(timer)
      }
    }
  }, [hasStreamingMessage, messages])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  // 处理容器点击事件（用于焦点管理）
  const handleContainerClick = () => {
    // 可以在这里添加焦点管理逻辑
  }

  // 手动回到底部
  const handleScrollToBottom = () => {
    setAutoScrollEnabled(true)
    setIsUserScrolling(false)
    scrollToBottom(false)
    console.log('👆 用户手动回到底部')
  }

  // 检查是否需要显示"回到底部"按钮
  const shouldShowScrollButton = !autoScrollEnabled || isUserScrolling

  // 检测新消息到达，重新启用自动滚动
  useEffect(() => {
    const currentMessageCount = messages.length
    if (currentMessageCount > lastMessageCount.current) {
      // 有新消息到达
      const newMessage = messages[currentMessageCount - 1]
      if (newMessage && newMessage.role === 'user') {
        // 用户发送新消息，重新启用自动滚动
        setAutoScrollEnabled(true)
        setIsUserScrolling(false)
        console.log('📨 新用户消息，重新启用自动滚动')
      }
    }
    lastMessageCount.current = currentMessageCount
  }, [messages.length])

  return (
    <motion.div
      ref={containerRef}
      className="flex flex-col h-full bg-transparent chat-container"
      onClick={handleContainerClick}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* 聊天消息区域 */}
      <div className="messages-area">
        {/* 消息列表容器 */}
        <div className="h-full overflow-hidden relative">
          {/* 渐变遮罩 - 顶部 */}
          <div className="absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-dark-950 to-transparent z-10 pointer-events-none" />
          
          {/* 消息列表 */}
          <div
            ref={messagesContainerRef}
            className="h-full overflow-y-auto px-4 py-2 chat-messages"
            onScroll={handleScroll}
          >
            {messages.length === 0 ? (
              // 空状态
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="flex flex-col items-center justify-center h-full text-center"
              >
                <div className="glass rounded-2xl p-8 max-w-md mx-auto">
                  <motion.div
                    animate={{ 
                      scale: [1, 1.05, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ 
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                    className="text-6xl mb-4"
                  >
                    🤖
                  </motion.div>
                  <h3 className="text-xl font-cyber text-gradient mb-3">
                    开始对话
                  </h3>
                  <p className="text-slate-400 text-sm leading-relaxed">
                    向我提问任何问题，我会尽力为您提供帮助。
                    <br />
                    支持多轮对话、代码解释、创意写作等功能。
                  </p>
                </div>
              </motion.div>
            ) : (
              // 消息列表
              <MessageList messages={messages} isThinking={isThinking} />
            )}
            
            {/* 移除重复的加载指示器，使用MessageList中的思考提示 */}
            
            {/* 滚动锚点 */}
            <div ref={messagesEndRef} />
          </div>

          {/* 回到底部按钮 */}
          <AnimatePresence>
            {shouldShowScrollButton && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="absolute bottom-12 right-6 z-20"
              >
                <button
                  onClick={handleScrollToBottom}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-neon-blue to-neon-purple text-white rounded-full shadow-lg hover:shadow-neon transition-all duration-300 hover:scale-105"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                  <span className="text-sm font-medium">回到底部</span>
                </button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 渐变遮罩 - 底部 */}
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-dark-950 to-transparent z-10 pointer-events-none" />
        </div>
      </div>

      {/* 输入区域 - 无底部留白 */}
      <div className="border-t border-dark-700 bg-gradient-to-r from-dark-900/50 to-dark-800/50 backdrop-blur-md">
        <div className="px-3 py-1">
          <MessageInput
            onSendMessage={onSendMessage}
            disabled={isLoading}
            placeholder="输入您的消息... (支持Markdown格式)"
            isGenerating={isGenerating}
            canContinue={canContinue}
            onStopGeneration={onStopGeneration}
            onContinueGeneration={onContinueGeneration}
          />

          {/* 底部提示信息 - 紧凑布局 */}
          <div className="flex items-center justify-center text-xs text-slate-500 space-x-2">
            <span className="w-1 h-1 bg-green-500 rounded-full animate-pulse"></span>
            <span>AI在线</span>
            <span>•</span>
            <span>Powered by XingHe Intelligence</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default ChatContainer
