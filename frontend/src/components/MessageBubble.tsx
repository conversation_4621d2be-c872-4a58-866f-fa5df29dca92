/**
 * 消息气泡组件
 * 渲染单个消息的气泡样式，支持用户和AI消息的不同样式
 */

import React, { useState, memo } from 'react'
import { motion } from 'framer-motion'
import { <PERSON><PERSON>, Tooltip, message as antdMessage } from 'antd'
import { CopyOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { ChatMessage } from '../services/chatService'
import TypewriterText from './TypewriterText'

interface MessageBubbleProps {
  message: ChatMessage
  isConsecutive?: boolean
}

// 智能体名称映射
const getAgentDisplayName = (agentName: string): string => {
  const agentNameMap: Record<string, string> = {
    'primary': 'Primary智能体',
    'critic': 'Critic智能体',
    'optimizer': 'Optimizer智能体',
    'security': 'Security智能体'
  }
  // 只对已知的智能体添加"智能体"后缀，避免用户消息被错误标记
  return agentNameMap[agentName] || agentName
}

// 智能体颜色映射
const getAgentColor = (agentName: string): string => {
  const agentColorMap: Record<string, string> = {
    'primary': 'from-neon-blue to-neon-purple',
    'critic': 'from-neon-green to-neon-yellow'
  }
  return agentColorMap[agentName] || 'from-neon-blue to-neon-purple'
}

/**
 * 消息气泡组件 - 使用memo优化性能
 */
const MessageBubble: React.FC<MessageBubbleProps> = memo(({
  message,
  isConsecutive = false
}) => {
  const [isHovered, setIsHovered] = useState(false)
  const isUser = message.role === 'user'

  // 复制消息内容
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      antdMessage.success('消息已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      antdMessage.error('复制失败，请手动选择文本')
    }
  }

  // Markdown渲染组件配置
  const markdownComponents = {
    // 代码块渲染
    code: ({ node, inline, className, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(className || '')
      const language = match ? match[1] : ''

      return !inline && language ? (
        <div className="relative group">
          <SyntaxHighlighter
            style={oneDark}
            language={language}
            PreTag="div"
            className="rounded-lg !bg-dark-900 !mt-2 !mb-2"
            showLineNumbers={true}
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
          <Button
            size="small"
            icon={<CopyOutlined />}
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={() => navigator.clipboard.writeText(String(children))}
          />
        </div>
      ) : (
        <code
          className="bg-dark-800 text-neon-blue px-1 py-0.5 rounded text-sm font-mono"
          {...props}
        >
          {children}
        </code>
      )
    },

    // 表格渲染 - 优化样式和显示
    table: ({ children }: any) => (
      <div className="overflow-x-auto my-4 rounded-lg border border-dark-600 bg-dark-800">
        <table className="min-w-full border-collapse">
          {children}
        </table>
      </div>
    ),

    thead: ({ children }: any) => (
      <thead className="bg-gradient-to-r from-dark-700 to-dark-600">
        {children}
      </thead>
    ),

    tbody: ({ children }: any) => (
      <tbody className="divide-y divide-dark-600">
        {children}
      </tbody>
    ),

    tr: ({ children }: any) => (
      <tr className="hover:bg-dark-700 transition-colors duration-200">
        {children}
      </tr>
    ),

    th: ({ children }: any) => (
      <th className="px-4 py-3 text-left font-semibold text-neon-blue bg-dark-700 border-r border-dark-600 last:border-r-0">
        {children}
      </th>
    ),

    td: ({ children }: any) => (
      <td className="px-4 py-3 text-slate-200 border-r border-dark-600 last:border-r-0 whitespace-nowrap">
        {children}
      </td>
    ),

    // 换行标签处理
    br: () => <br />,
    
    // 段落渲染
    p: ({ children }: any) => (
      <p className="mb-2 last:mb-0 leading-relaxed">{children}</p>
    ),
    
    // 列表渲染
    ul: ({ children }: any) => (
      <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
    ),
    
    ol: ({ children }: any) => (
      <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
    ),
    
    // 链接渲染
    a: ({ href, children }: any) => (
      <a 
        href={href} 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-neon-blue hover:text-neon-purple underline transition-colors"
      >
        {children}
      </a>
    ),
    
    // 强调文本
    strong: ({ children }: any) => (
      <strong className="font-semibold text-white">{children}</strong>
    ),
    
    // 斜体文本
    em: ({ children }: any) => (
      <em className="italic text-slate-300">{children}</em>
    ),
    
    // 引用块
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-neon-blue pl-4 italic text-slate-300 my-2">
        {children}
      </blockquote>
    ),
  }

  return (
    <motion.div
      className={`relative group ${isUser ? 'ml-auto' : 'mr-auto'}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {/* 头像（仅在非连续消息时显示） */}
      {!isConsecutive && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className={`absolute top-0 ${
            isUser ? '-right-12' : '-left-12'
          } w-8 h-8 rounded-full flex items-center justify-center ${
            isUser 
              ? 'bg-gradient-to-r from-neon-blue to-neon-purple' 
              : 'bg-gradient-to-r from-neon-green to-neon-blue'
          } shadow-neon`}
        >
          {isUser ? (
            <UserOutlined className="text-white text-sm" />
          ) : (
            <RobotOutlined className="text-white text-sm" />
          )}
        </motion.div>
      )}

      {/* 消息气泡 */}
      <motion.div
        className={`relative rounded-2xl shadow-lg ${
          isUser
            ? 'bg-gradient-to-r from-neon-blue to-neon-purple text-white rounded-br-md'
            : 'glass border border-neon-blue border-opacity-30 text-slate-100 rounded-bl-md'
        } ${isConsecutive ? (isUser ? 'rounded-tr-md' : 'rounded-tl-md') : ''}`}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* AI消息智能体标识头部 */}
        {!isUser && message.agentName && (
          <div className={`px-3 py-2 rounded-t-2xl bg-gradient-to-r ${getAgentColor(message.agentName)} ${
            isConsecutive ? 'rounded-tl-md' : ''
          }`}>
            <div className="flex items-center space-x-2">
              <RobotOutlined className="text-white text-sm" />
              <span className="text-white text-sm font-medium">
                {getAgentDisplayName(message.agentName)}
              </span>
            </div>
          </div>
        )}

        {/* 消息内容 */}
        <div className={`px-4 py-3 ${!isUser && message.agentName ? 'rounded-t-none' : ''}`}>
          <div className="max-w-none">
            {isUser ? (
              // 用户消息：纯文本显示
              <p className="mb-0 whitespace-pre-wrap break-words text-white">
                {message.content}
              </p>
            ) : (
              // AI消息：打字机效果 + Markdown渲染
              <TypewriterText
                content={message.content}
                isStreaming={message.isStreaming || false}
                speed={30}
                markdownComponents={markdownComponents}
              />
            )}
          </div>
        </div>



        {/* 悬浮操作按钮 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ 
            opacity: isHovered ? 1 : 0,
            scale: isHovered ? 1 : 0.8
          }}
          transition={{ duration: 0.2 }}
          className={`absolute top-2 ${
            isUser ? 'left-2' : 'right-2'
          } flex space-x-1`}
        >
          <Tooltip title="复制消息">
            <Button
              size="small"
              type="text"
              icon={<CopyOutlined />}
              onClick={handleCopy}
              className="text-slate-400 hover:text-white bg-black bg-opacity-50 hover:bg-opacity-70 border-none"
            />
          </Tooltip>
        </motion.div>


      </motion.div>

      {/* 消息尾巴（箭头） */}
      <div className={`absolute top-4 ${
        isUser 
          ? 'right-0 translate-x-1' 
          : 'left-0 -translate-x-1'
      } w-0 h-0 ${
        isUser
          ? 'border-l-8 border-l-neon-purple border-t-4 border-t-transparent border-b-4 border-b-transparent'
          : 'border-r-8 border-r-dark-700 border-t-4 border-t-transparent border-b-4 border-b-transparent'
      }`} />
    </motion.div>
  )
})

// 设置displayName用于调试
MessageBubble.displayName = 'MessageBubble'

export default MessageBubble
