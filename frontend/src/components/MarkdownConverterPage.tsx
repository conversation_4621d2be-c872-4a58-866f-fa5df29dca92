/**
 * Markdown转换器页面组件
 * 提供文件上传和Markdown转换的完整界面
 */

import React, { useState } from 'react'
import { Card, Row, Col, Typography, Space, Divider } from 'antd'
import { FileMarkdownOutlined, UploadOutlined } from '@ant-design/icons'
import { motion } from 'framer-motion'
import FileUpload from './FileUpload'
import FileToMarkdownConverter from './FileToMarkdownConverter'

const { Title, Paragraph, Text } = Typography

interface UploadedFileInfo {
  file_id: string
  original_name: string
  file_size: number
  content_type: string
  upload_time: string
}

const MarkdownConverterPage: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<UploadedFileInfo | null>(null)
  const [showConverter, setShowConverter] = useState(false)

  // 处理文件上传成功
  const handleFileUpload = (fileInfo: UploadedFileInfo) => {
    console.log('文件上传成功:', fileInfo)
    setSelectedFile(fileInfo)
    setShowConverter(true)
  }

  // 处理文件删除
  const handleFileDelete = (fileId: string) => {
    if (selectedFile?.file_id === fileId) {
      setSelectedFile(null)
      setShowConverter(false)
    }
  }

  // 支持的文件类型说明
  const supportedTypes = [
    { type: 'PDF文档', extensions: '.pdf', description: '使用Marker进行高质量转换，保留格式和结构' },
    { type: '图片文件', extensions: '.jpg, .png, .gif, .bmp, .webp', description: '转换为带图片嵌入的Markdown' },
    { type: 'Word文档', extensions: '.docx, .doc', description: '提取文本内容并保留基本格式' },
    { type: 'Excel表格', extensions: '.xlsx, .xls', description: '转换为Markdown表格格式' },
    { type: '文本文件', extensions: '.txt', description: '包装为代码块格式' },
    { type: 'CSV文件', extensions: '.csv', description: '转换为Markdown表格' },
    { type: 'JSON文件', extensions: '.json', description: '格式化为代码块' },
    { type: 'Markdown文件', extensions: '.md', description: '直接读取原始内容' }
  ]

  return (
    <div className="markdown-converter-page p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-8"
      >
        <Title level={2} className="flex items-center justify-center">
          <FileMarkdownOutlined className="text-purple-500 mr-3" />
          文件到Markdown转换器
        </Title>
        <Paragraph className="text-lg text-gray-600">
          支持多种文件格式转换为Markdown格式，使用Marker库提供高质量PDF转换
        </Paragraph>
      </motion.div>

      <Row gutter={[24, 24]}>
        {/* 左侧：文件上传区域 */}
        <Col xs={24} lg={12}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card
              title={
                <span>
                  <UploadOutlined className="mr-2" />
                  文件上传
                </span>
              }
              className="h-full"
            >
              <FileUpload
                onFileUpload={handleFileUpload}
                onFileDelete={handleFileDelete}
                maxFiles={1}
              />
              
              <Divider />
              
              {/* 支持的文件类型 */}
              <div>
                <Text strong className="block mb-3">支持的文件类型：</Text>
                <Space direction="vertical" size="small" className="w-full">
                  {supportedTypes.map((item, index) => (
                    <div key={index} className="p-2 bg-gray-50 rounded">
                      <Text strong className="text-blue-600">{item.type}</Text>
                      <Text className="block text-xs text-gray-500">{item.extensions}</Text>
                      <Text className="block text-sm">{item.description}</Text>
                    </div>
                  ))}
                </Space>
              </div>
            </Card>
          </motion.div>
        </Col>

        {/* 右侧：Markdown转换结果 */}
        <Col xs={24} lg={12}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {showConverter && selectedFile ? (
              <FileToMarkdownConverter
                fileInfo={selectedFile}
                visible={showConverter}
                onClose={() => setShowConverter(false)}
              />
            ) : (
              <Card
                title="Markdown转换结果"
                className="h-full"
                bodyStyle={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  minHeight: '400px'
                }}
              >
                <div className="text-center text-gray-400">
                  <FileMarkdownOutlined style={{ fontSize: '64px', marginBottom: '16px' }} />
                  <Paragraph>请先上传文件以开始转换</Paragraph>
                </div>
              </Card>
            )}
          </motion.div>
        </Col>
      </Row>

      {/* 功能特性说明 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-8"
      >
        <Card title="功能特性">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <div className="text-center">
                <div className="text-2xl mb-2">🚀</div>
                <Text strong>高质量转换</Text>
                <Paragraph className="text-sm text-gray-600">
                  使用Marker库进行PDF转换，保留原始格式和结构
                </Paragraph>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="text-center">
                <div className="text-2xl mb-2">📄</div>
                <Text strong>多格式支持</Text>
                <Paragraph className="text-sm text-gray-600">
                  支持PDF、Word、Excel、图片等多种常见文件格式
                </Paragraph>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="text-center">
                <div className="text-2xl mb-2">💾</div>
                <Text strong>便捷导出</Text>
                <Paragraph className="text-sm text-gray-600">
                  支持复制到剪贴板和下载Markdown文件
                </Paragraph>
              </div>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <div className="text-center">
                <div className="text-2xl mb-2">👀</div>
                <Text strong>实时预览</Text>
                <Paragraph className="text-sm text-gray-600">
                  提供Markdown预览和源码查看功能
                </Paragraph>
              </div>
            </Col>
          </Row>
        </Card>
      </motion.div>
    </div>
  )
}

export default MarkdownConverterPage
