/**
 * 侧边栏组件
 * 包含对话历史、设置和其他功能
 */

import React, { useState } from 'react'
import { But<PERSON>, Divider, Tooltip, Modal, List, Typography } from 'antd'
import {
  DeleteOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  HistoryOutlined,
  MessageOutlined,
  RobotOutlined
} from '@ant-design/icons'
import { motion } from 'framer-motion'
import { Conversation } from '../services/chatService'

const { Text } = Typography

interface SidebarProps {
  onClearMessages: () => void
  messageCount: number
  conversationId: string
  conversations: Conversation[]
  onNewConversation: () => void
  onSwitchConversation: (conversationId: string) => void
  onDeleteConversation: (conversationId: string) => void
  isInNewConversation?: boolean // 是否处于新对话状态
}

/**
 * 侧边栏组件
 */
const Sidebar: React.FC<SidebarProps> = ({
  onClearMessages,
  messageCount,
  conversationId,
  conversations,
  onNewConversation,
  onSwitchConversation,
  onDeleteConversation,
  isInNewConversation = false
}) => {
  const [showAbout, setShowAbout] = useState(false)
  const [showConversations, setShowConversations] = useState(false)

  // 确认清空所有对话
  const handleClearMessages = () => {
    Modal.confirm({
      title: '确认清空所有对话',
      content: (
        <div>
          <p>此操作将删除所有对话记录，包括：</p>
          <ul className="mt-2 ml-4 text-slate-400">
            <li>• 所有历史对话</li>
            <li>• 所有聊天消息</li>
            <li>• 当前对话内容</li>
          </ul>
          <p className="mt-3 text-red-400 font-medium">此操作无法撤销，确定要继续吗？</p>
        </div>
      ),
      okText: '确认清空',
      cancelText: '取消',
      okType: 'danger',
      onOk: onClearMessages,
      width: 480,
    })
  }

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (days === 0) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    } else if (days === 1) {
      return '昨天'
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
    }
  }

  // 侧边栏菜单项
  const menuItems = [
    {
      key: 'new-chat',
      icon: <PlusOutlined />,
      label: '开启新对话',
      onClick: onNewConversation,
      disabled: isInNewConversation,
      tooltip: isInNewConversation ? '当前已是新对话，请先发送消息' : '开始一个全新的对话'
    },
    {
      key: 'recent-chats',
      icon: <HistoryOutlined />,
      label: '最近对话',
      onClick: () => setShowConversations(true),
      disabled: conversations.length === 0,
      tooltip: conversations.length === 0 ? '暂无对话记录' : `查看最近的对话记录 (${conversations.length}条)`
    },
    {
      key: 'clear',
      icon: <DeleteOutlined />,
      label: '清空所有对话',
      onClick: handleClearMessages,
      disabled: conversations.length === 0,
      tooltip: conversations.length === 0 ? '暂无对话记录' : `删除所有对话记录 (${conversations.length}条)`
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => console.log('打开设置'),
      tooltip: '应用设置（开发中）'
    },
    {
      key: 'about',
      icon: <InfoCircleOutlined />,
      label: '关于',
      onClick: () => setShowAbout(true),
      tooltip: '关于星河智能'
    }
  ]

  return (
    <>
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: 0 }}
        exit={{ x: -300 }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="w-80 h-screen bg-dark-950/95 backdrop-blur-md border-r border-dark-700 shadow-2xl flex flex-col"
      >
        {/* 侧边栏头部 */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="pt-6 px-6 pb-6 border-b border-dark-700"
        >
          <div className="flex items-center space-x-3 mb-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="w-10 h-10 rounded-full bg-gradient-to-r from-neon-blue to-neon-purple flex items-center justify-center shadow-neon"
            >
              <RobotOutlined className="text-white text-lg" />
            </motion.div>
            <div className="flex flex-col justify-center">
              <h2 className="text-lg font-cyber font-bold text-gradient leading-tight">
                星河智能
              </h2>
              <p className="text-xs text-slate-400 leading-tight">
                智能对话助手
              </p>
            </div>
          </div>

          {/* 对话统计 */}
          <div className="glass rounded-lg p-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">当前对话</span>
              <span className="text-neon-blue font-mono">
                {messageCount} 条消息
              </span>
            </div>
            <div className="flex items-center justify-between text-xs mt-2">
              <span className="text-slate-500">对话ID</span>
              <span className="text-slate-500 font-mono truncate ml-2">
                {conversationId.slice(-8)}
              </span>
            </div>
          </div>
        </motion.div>

        {/* 菜单列表 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="flex-1 p-4 space-y-2"
        >
          <h3 className="text-sm font-medium text-slate-400 mb-3 px-2">
            功能菜单
          </h3>
          
          {menuItems.map((item, index) => (
            <motion.div
              key={item.key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <Tooltip title={item.tooltip} placement="right">
                <Button
                  type="text"
                  icon={item.icon}
                  onClick={item.onClick}
                  disabled={item.disabled}
                  className={`w-full justify-start text-left h-12 px-4 ${
                    item.disabled 
                      ? 'text-slate-600 cursor-not-allowed' 
                      : 'text-slate-300 hover:text-neon-blue hover:bg-neon-blue/10'
                  } border-none rounded-lg transition-all duration-200`}
                >
                  <span className="ml-3">{item.label}</span>
                </Button>
              </Tooltip>
            </motion.div>
          ))}
        </motion.div>

        <Divider className="border-dark-700 my-0" />

        {/* 侧边栏底部 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="p-4 space-y-3"
        >
          {/* 技术栈信息 */}
          {/*<div className="glass rounded-lg p-3 text-xs text-slate-400">*/}
          {/*  <div className="flex items-center justify-between mb-2">*/}
          {/*    <span>技术栈</span>*/}
          {/*    <HeartOutlined className="text-red-500" />*/}
          {/*  </div>*/}
          {/*  <div className="space-y-1">*/}
          {/*    <div>• React + TypeScript</div>*/}
          {/*    <div>• Ant Design X</div>*/}
          {/*    <div>• FastAPI + Autogen</div>*/}
          {/*    <div>• Tailwind CSS</div>*/}
          {/*  </div>*/}
          {/*</div>*/}

          {/* GitHub链接 */}
          <Button
            type="text"
            // icon={<GithubOutlined />}
            className="w-full text-slate-400 hover:text-neon-blue border-none"
            // onClick={() => window.open('https://github.com/microsoft/autogen', '_blank')}
          >
            欢迎使用星河智能
          </Button>
        </motion.div>
      </motion.div>

      {/* 最近对话对话框 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <HistoryOutlined className="text-neon-blue" />
            <span>最近对话</span>
          </div>
        }
        open={showConversations}
        onCancel={() => setShowConversations(false)}
        footer={null}
        width={600}
        className="cyber-modal"
      >
        <div className="max-h-96 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="text-center py-8 text-slate-400">
              <MessageOutlined className="text-4xl mb-4" />
              <p>暂无对话记录</p>
              <p className="text-sm">开始一个新对话吧！</p>
            </div>
          ) : (
            <List
              dataSource={conversations}
              renderItem={(conversation) => (
                <List.Item
                  className={`cursor-pointer hover:bg-slate-800/50 rounded-lg p-3 transition-colors ${
                    conversation.id === conversationId ? 'bg-neon-blue/10 border border-neon-blue/30' : ''
                  }`}
                  onClick={() => {
                    onSwitchConversation(conversation.id)
                    setShowConversations(false)
                  }}
                  actions={[
                    <Button
                      key="delete"
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      className="text-red-400 hover:text-red-300"
                      onClick={(e) => {
                        e.stopPropagation()
                        Modal.confirm({
                          title: '删除对话',
                          content: '确定要删除这个对话吗？此操作无法撤销。',
                          okText: '删除',
                          cancelText: '取消',
                          okType: 'danger',
                          onOk: () => onDeleteConversation(conversation.id)
                        })
                      }}
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Text className="text-slate-200" ellipsis>
                        {conversation.title}
                      </Text>
                    }
                    description={
                      <div className="text-slate-400 text-sm">
                        <div>
                          {conversation.user_message_count || 0} 条用户消息，
                          {conversation.message_count} 条总消息
                        </div>
                        <div>{formatTime(conversation.updated_at)}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>
      </Modal>

      {/* 关于对话框 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <RobotOutlined className="text-neon-blue" />
            <span>关于星河智能</span>
          </div>
        }
        open={showAbout}
        onCancel={() => setShowAbout(false)}
        footer={[
          <Button key="close" onClick={() => setShowAbout(false)}>
            关闭
          </Button>
        ]}
        className="cyber-modal"
      >
        <div className="space-y-4 text-slate-300">
          <p>
            <strong className="text-neon-blue">星河智能</strong> 是一个基于 Microsoft Autogen 框架构建的智能对话系统。
          </p>
          
          <div>
            <h4 className="text-white font-medium mb-2">✨ 主要特性</h4>
            <ul className="space-y-1 text-sm">
              <li>• 实时流式对话体验</li>
              <li>• 支持 Markdown 格式渲染</li>
              <li>• 炫酷的科技感界面设计</li>
              <li>• 响应式布局适配</li>
              <li>• 基于 Autogen 0.5.7</li>
            </ul>
          </div>

          <div>
            <h4 className="text-white font-medium mb-2">🛠️ 技术架构</h4>
            <ul className="space-y-1 text-sm">
              <li>• 前端：React + TypeScript + Ant Design X</li>
              <li>• 后端：FastAPI + Autogen</li>
              <li>• 样式：Tailwind CSS + Framer Motion</li>
              <li>• 通信：SSE 流式协议</li>
            </ul>
          </div>

          <div className="text-center pt-4 border-t border-dark-700">
            <p className="text-sm text-slate-400">
              版本 1.0.0 • 构建于 {new Date().getFullYear()}
            </p>
          </div>
        </div>
      </Modal>
    </>
  )
}

export default Sidebar
