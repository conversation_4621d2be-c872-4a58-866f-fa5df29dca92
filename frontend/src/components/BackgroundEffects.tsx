/**
 * 背景特效组件
 * 提供炫酷的背景动画效果，包括粒子、网格和渐变
 */

import React, { useEffect, useRef } from 'react'


/**
 * 背景特效组件
 */
const BackgroundEffects: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // 粒子系统
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    const resizeCanvas = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }
    resizeCanvas()
    window.addEventListener('resize', resizeCanvas)

    // 粒子类
    class Particle {
      x: number
      y: number
      vx: number
      vy: number
      size: number
      opacity: number
      color: string

      constructor() {
        this.x = Math.random() * (canvas?.width || 800)
        this.y = Math.random() * (canvas?.height || 600)
        this.vx = (Math.random() - 0.5) * 0.5
        this.vy = (Math.random() - 0.5) * 0.5
        this.size = Math.random() * 2 + 0.5
        this.opacity = Math.random() * 0.5 + 0.2
        this.color = Math.random() > 0.5 ? '#00d4ff' : '#8b5cf6'
      }

      update() {
        this.x += this.vx
        this.y += this.vy

        // 边界检测
        const canvasWidth = canvas?.width || 800
        const canvasHeight = canvas?.height || 600
        if (this.x < 0 || this.x > canvasWidth) this.vx *= -1
        if (this.y < 0 || this.y > canvasHeight) this.vy *= -1

        // 保持在画布内
        this.x = Math.max(0, Math.min(canvasWidth, this.x))
        this.y = Math.max(0, Math.min(canvasHeight, this.y))
      }

      draw() {
        if (!ctx) return
        
        ctx.save()
        ctx.globalAlpha = this.opacity
        ctx.fillStyle = this.color
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
        ctx.restore()
      }
    }

    // 创建粒子
    const particles: Particle[] = []
    const particleCount = Math.min(50, Math.floor((canvas?.width || 800) * (canvas?.height || 600) / 15000))
    
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle())
    }

    // 动画循环
    let animationId: number
    const animate = () => {
      if (!canvas || !ctx) return
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 更新和绘制粒子
      particles.forEach(particle => {
        particle.update()
        particle.draw()
      })

      // 绘制连接线
      particles.forEach((particle, i) => {
        particles.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x
          const dy = particle.y - otherParticle.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 100) {
            ctx.save()
            ctx.globalAlpha = (100 - distance) / 100 * 0.1
            ctx.strokeStyle = '#00d4ff'
            ctx.lineWidth = 0.5
            ctx.beginPath()
            ctx.moveTo(particle.x, particle.y)
            ctx.lineTo(otherParticle.x, otherParticle.y)
            ctx.stroke()
            ctx.restore()
          }
        })
      })

      animationId = requestAnimationFrame(animate)
    }

    animate()

    return () => {
      window.removeEventListener('resize', resizeCanvas)
      cancelAnimationFrame(animationId)
    }
  }, [])

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      {/* 渐变背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800" />

      {/* 网格背景 */}
      <div className="absolute inset-0 bg-grid opacity-20" />

      {/* 粒子画布 */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
      />

      {/* 简化的光晕效果 */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-blue rounded-full blur-3xl opacity-10 animate-pulse-slow" />
      <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-neon-purple rounded-full blur-3xl opacity-10 animate-pulse-slow" style={{ animationDelay: '2s' }} />

      {/* 简化的边框效果 */}
      <div className="absolute inset-0 border border-neon-blue border-opacity-20 pointer-events-none animate-pulse-slow" />

      {/* 简化的角落装饰 */}
      <div className="absolute top-0 left-0 w-32 h-32 border-l-2 border-t-2 border-neon-blue border-opacity-30" />
      <div className="absolute top-0 right-0 w-32 h-32 border-r-2 border-t-2 border-neon-purple border-opacity-30" />
      <div className="absolute bottom-0 left-0 w-32 h-32 border-l-2 border-b-2 border-neon-green border-opacity-30" />
      <div className="absolute bottom-0 right-0 w-32 h-32 border-r-2 border-b-2 border-neon-pink border-opacity-30" />
    </div>
  )
}

export default BackgroundEffects
