/**
 * 加载指示器组件
 * 显示AI正在思考的动画效果
 */

import React from 'react'
import { motion } from 'framer-motion'
import { RobotOutlined } from '@ant-design/icons'

/**
 * 加载指示器组件
 */
const LoadingIndicator: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex items-center space-x-3 p-4 glass rounded-2xl rounded-bl-md border border-neon-blue border-opacity-30 max-w-xs"
    >
      {/* AI头像 */}
      <motion.div
        animate={{ 
          scale: [1, 1.1, 1],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="w-8 h-8 rounded-full bg-gradient-to-r from-neon-green to-neon-blue flex items-center justify-center shadow-neon"
      >
        <RobotOutlined className="text-white text-sm" />
      </motion.div>

      {/* 思考动画 */}
      <div className="flex flex-col space-y-2">
        {/* 主要文本 */}
        <div className="flex items-center space-x-2">
          <span className="text-slate-300 text-sm">AI正在思考</span>
          <div className="flex space-x-1">
            <motion.div
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                delay: 0
              }}
              className="w-1 h-1 bg-neon-blue rounded-full"
            />
            <motion.div
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                delay: 0.2
              }}
              className="w-1 h-1 bg-neon-blue rounded-full"
            />
            <motion.div
              animate={{ opacity: [0.3, 1, 0.3] }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                delay: 0.4
              }}
              className="w-1 h-1 bg-neon-blue rounded-full"
            />
          </div>
        </div>

        {/* 进度条动画 */}
        <div className="w-32 h-1 bg-dark-700 rounded-full overflow-hidden">
          <motion.div
            animate={{ x: [-100, 100] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="h-full w-8 bg-gradient-to-r from-transparent via-neon-blue to-transparent"
          />
        </div>
      </div>
    </motion.div>
  )
}

export default LoadingIndicator
