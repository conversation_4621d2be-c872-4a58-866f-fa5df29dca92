#!/usr/bin/env python3
"""
系统测试脚本
测试后端API和Autogen服务是否正常工作
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# 添加backend目录到Python路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from autogen_service import AutogenService
    from models import ChatRequest
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


async def test_autogen_service():
    """测试Autogen服务"""
    print("🧪 测试 Autogen 服务...")
    
    try:
        # 创建服务实例
        service = AutogenService()
        print("✅ Autogen服务创建成功")
        
        # 测试普通聊天
        print("\n📝 测试普通聊天...")
        response = await service.chat("你好，请简单介绍一下你自己")
        print(f"✅ 普通聊天响应: {response[:100]}...")
        
        # 测试流式聊天
        print("\n🌊 测试流式聊天...")
        print("📝 流式响应: ", end="")
        chunk_count = 0
        async for chunk in service.chat_stream("写一首关于春天的短诗"):
            print(chunk, end="", flush=True)
            chunk_count += 1
            if chunk_count > 50:  # 限制输出长度
                break
        print(f"\n✅ 流式聊天完成，收到 {chunk_count} 个数据块")
        
        # 关闭服务
        await service.close()
        print("✅ 服务关闭成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True


async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试 API 端点...")
    
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            # 测试健康检查
            print("📡 测试健康检查端点...")
            async with session.get('http://localhost:8000/health') as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 健康检查成功: {data}")
                else:
                    print(f"❌ 健康检查失败: {resp.status}")
                    return False
            
            # 测试根端点
            print("📡 测试根端点...")
            async with session.get('http://localhost:8000/') as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 根端点成功: {data}")
                else:
                    print(f"❌ 根端点失败: {resp.status}")
                    return False
            
            # 测试聊天端点
            print("📡 测试聊天端点...")
            chat_data = {
                "message": "你好，这是一个测试消息",
                "conversation_id": "test_conv_123",
                "user_id": "test_user"
            }
            async with session.post('http://localhost:8000/chat', json=chat_data) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 聊天端点成功: {data['message'][:50]}...")
                else:
                    print(f"❌ 聊天端点失败: {resp.status}")
                    return False
                    
    except ImportError:
        print("⚠️ 跳过API测试 (需要安装 aiohttp: pip install aiohttp)")
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False
    
    return True


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro} (需要 >= 3.8)")
        return False
    
    # 检查环境变量文件
    env_file = backend_path / ".env"
    if env_file.exists():
        print("✅ 环境变量文件存在")
        
        # 读取并验证环境变量
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.read()
            if 'API_KEY' in env_content and 'BASE_URL' in env_content:
                print("✅ 环境变量配置完整")
            else:
                print("⚠️ 环境变量可能不完整")
    else:
        print("❌ 环境变量文件不存在")
        return False
    
    # 检查依赖包
    try:
        import fastapi
        import uvicorn
        import autogen_agentchat
        print("✅ 主要依赖包已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    print("=" * 50)
    print("🚀 Autogen Chat 系统测试")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请修复后重试")
        return
    
    # 测试Autogen服务
    if not await test_autogen_service():
        print("\n❌ Autogen服务测试失败")
        return
    
    # 测试API端点（如果后端正在运行）
    print("\n" + "=" * 30)
    print("注意: API测试需要后端服务运行在 localhost:8000")
    print("如果后端未启动，API测试将失败")
    print("=" * 30)
    
    await test_api_endpoints()
    
    print("\n" + "=" * 50)
    print("🎉 系统测试完成！")
    print("=" * 50)
    print("\n💡 下一步:")
    print("1. 启动后端: python backend/main.py")
    print("2. 启动前端: cd frontend && npm run dev")
    print("3. 访问: http://localhost:3000")


if __name__ == "__main__":
    asyncio.run(main())
