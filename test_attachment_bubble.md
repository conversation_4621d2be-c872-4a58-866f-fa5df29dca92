# 附件气泡测试文档

这是一个用于测试附件气泡功能的Markdown文档。

## 功能说明

当用户发送带有附件的消息时，系统应该：

1. **附件独立显示**：附件显示为独立的气泡，位于文本消息上方
2. **点击预览**：点击附件气泡可以打开文件预览
3. **文件信息**：显示文件名、大小和类型图标
4. **视觉效果**：附件气泡有独特的样式和悬浮效果

## 测试场景

### 场景1：单个附件 + 文本消息
- 用户输入："请分析这个文档"
- 上传附件：test_attachment_bubble.md
- 期望：附件气泡在上方，文本消息在下方

### 场景2：多个附件 + 文本消息
- 用户输入："请对比这些文件"
- 上传多个附件：PDF、Word、Excel等
- 期望：多个附件气泡垂直排列，文本消息在最下方

### 场景3：仅附件，无文本
- 用户不输入文本，只上传附件
- 期望：只显示附件气泡，不显示空的文本气泡

## 预期效果

- ✅ 附件气泡样式美观，与用户消息气泡区分明显
- ✅ 文件图标根据类型正确显示
- ✅ 文件大小格式化显示
- ✅ 点击附件可以打开预览
- ✅ 悬浮时有视觉反馈

测试完成！
